/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <vector>
#include <map>
using namespace std;
#include <ml_ctrlsub.h>
#include <time.h>
//#include <sqlca.h>
//#include <sql2oci.h>

#define SIZE_OF_SQL_MSG        1024
#define SIZE_OF_QUEUE_SQL_MSG  1024
#define SIZE_OF_MSG_ID         50+1
#define SIZE_OF_CMMS_ID        32+1
#define SIZE_OF_DLV_DATE       14+1
#define SIZE_OF_SND_NUMBER     12+1
#define SIZE_OF_RCV_NUMBER     12+1
#define SIZE_OF_RES_CODE       4+1
#define SIZE_OF_RES_TEXT       200+1
#define SIZE_OF_END_TELCO      5+1

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck);
	int setReportData(int telcoid, sql_context ctx, map<string, string>& mapReport);
	int setPollingReportData(int telcoid, sql_context ctx, map<string, string>& mapReport);
	int setPollingReport_batch(int telcoid, sql_context ctx, int succSize,
                               char _msg_id[][51], char _dlv_date[][15], char _res_code[][5], char _res_text[][201],
                               char _end_telco[][6]);
                               
	//int getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend);
	long long getMsgData(sql_context ctx, char *q_name, vector<string> &vtSend);
	long long getMsgDataBt(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V2(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V3(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V4(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V5(sql_context ctx, char *q_name, map<string,string> &mapSend);
	int getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData);
//	int _putMsgRetryDB(int mmsid, char *q_name, int telcoid, sql_context ctx);
	int _putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx);

private:
	
	void sql_error(sql_context ctx, int* result);
	bool m_bThread;
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
