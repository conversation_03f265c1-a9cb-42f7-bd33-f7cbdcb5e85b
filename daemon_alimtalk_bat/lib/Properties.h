/*
 * Properties.h
 *
 *  Created on: 2009. 8. 21
 *      Author: 임동규
 */

#ifndef PROPERTIES_H_
#define PROPERTIES_H_

namespace KSKYB {

typedef struct Q_Entry {
	char *name;
	char *value;
	struct Q_Entry *next;
} Q_Entry;

class CProperties
{
public:
	CProperties();
	virtual ~CProperties();

	void load(char* szFile);
	char* getProperty(const char* szKey);
	int getPropertyInt(const char* szKey);

private:
	Q_Entry *first;
	char* readFile(char *szFile, int *size);
	Q_Entry* decodeEntry(char *szEntry);
	char* removeSpace(char *str);
	char* makeWord(char* str, char stop);
};

}

#endif /* PROPERTIES_H_ */
