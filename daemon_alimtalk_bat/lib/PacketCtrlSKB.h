/*
 * PacketCtrlSKB.h
 *
 *  Created on: 2011. 07. 27.
 *      Author: Administrator
 */

#ifndef PacketCtrlSKB_H_
#define PacketCtrlSKB_H_

#include <string>
#include <vector>
#include <arpa/inet.h>
#include <iostream>
#include <ml_ctrlsub.h>

using namespace std;

typedef struct _HEADER {			/* KskyB 전문 Header */
	char szJobCode[2];			//job code(S1,A2,D2,P2,O3,C2)
	char szInvoketype[2];		//Request - 00, response - 01
	char szVersion[8];			//protocol version(최소 버전 02.10.00) 예) 02.10.00
	char szClientType[2];		//Client 종류 00:DB Agent, 01:WEB(개별전송), 02:WEB(대량발송), 03:OAM, 04:Excel Messager, 05:API, 06:직접연동 %직접연동이므로 06으로 설정
	char szClientVersion[8];	//Client version(최소 버전 02.00.00) 예) 02.00.00
	char szTID[10];				//transactio ID. Client 최초 실행시, 최대값 이상일때 reset됨.(0000000000 ~ 9999999999)
	char szBodySize[10];		//total body size (0000000000 ~ 9999999999)
} HEADER;

typedef struct MSG_AUTH_BIND_SND	{		/* 접속요청 전문 [Client -> KskyB] */
	HEADER header;

	char szMID[16];	//id
	char szSID[16];	//aid
	char szPWD[32];	//passwd
} AUTH_BIND_REQ;

typedef struct MSG_AUTH_BIND_ACK {		/* 접속요청에 대한 응답 [SKB -> Client] */
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
	char Tps[4];
	char ServerCount[4];
	char submitserverIP1[16];
	char submitserverport1[5];
	char submitserverIP2[16];
	char submitserverport2[5];
} AUTH_BIND_ACK;

typedef struct MSG_BIND_SND	{		/* 접속요청 전문 [Client -> KskyB] */
	HEADER header;

	char szMID[16];	//id
	char szSID[16];	//aid
	char szPWD[32];	//passwd
} BIND_REQ;

typedef struct MSG_BIND_ACK {		/* 접속요청에 대한 응답 [KT -> Client] */
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
} BIND_ACK;

typedef struct MSG_DATA_SND	{		/* 메세지 전송 전문 [Client -> KT] */
	HEADER header;

	char cmp_msg_id[20];
	char rcv_phn_id[24];
	char odr_fg[1];
	char sms_gb[1];
	char used_cd[2];
	char reserved_fg[1];
	char reserved_dttm[14];
	char cmp_snd_dttm[14];
	char saved_fg[1];
	char snd_phn_id[24];
	char NAT_CD[8];
	char assign_cd[5];
	char sender_id[20];
	char sender_value[32];
	char msg[200];
	char callback_url[200];
	char bdata_count[2];
	char cp_id[16];
	char cp_content_type[4];
	char content_id[16];
	char content_price[8];
} DELIVER_REQ; /* header 42 byte body 613 = 655 */

typedef struct MSG_DATA_ACK {		/* 응답전문 [KT -> Client] */
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
	char cmp_msg_id[20];
	char snd_phn_id[24];
	char rcv_phn_id[24];
} DELIVER_ACK;

typedef struct MSG_TRNS_REP	{		/* 메세지 단말기 수신결과 전문 (레포트 전문 ) [KT -> Client] */
	HEADER header;

	char cmp_msg_id[20];
	char snd_phn_id[24];
	char rcv_phn_id[24];
	char reg_snd_dttm[14];
	char reg_rcv_dttm[14];
	char rslt_val[4];
	char telco_id[4];
} REPORT;

typedef struct MSG_REPORT_ACK {		/* 응답전문 [Client -> KT] */
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
	char cmp_msg_id[20];
} REPORT_ACK;

typedef struct MSG_LINK_CHK	{
	HEADER header;

} LINK_CHK;

typedef struct MSG_LINK_CHK_ACK	{
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
} LINK_CHK_ACK;

typedef struct MSG_CONTROL	{
	HEADER header;

	char ControlCommand[4];
	char ControlValue[4];
} CONTROL;

typedef struct MSG_CONTROL_ACK	{
	HEADER header;

	char ResultCode[4];
	char ResultMessage[32];
} CONTROL_ACK;

typedef struct tag_resultTable {
    char tCode[8];
    char kCode[8];
    char desc[512];
} CODETABLE;

class CPacketCtrlSKB
{
public:
	enum logLevels {
		TYPE_AUTH_BIND_REQ = 2,	/* AUTH Bind 요구		*/
		TYPE_AUTH_BIND_ACK,		/* AUTH Bind 응답		*/
		TYPE_BIND_REQ,	/* Bind 요구		*/
		TYPE_BIND_ACK,		/* Bind 응답		*/
		TYPE_DELIVER_REQ,	/* 전송요청		*/
		TYPE_DELIVER_ACK,	/* SubmitAck	*/
		TYPE_PING,			/* 상태확인		*/
		TYPE_PONG,			/* 상태확인Ack	*/
		TYPE_REPORT,		/* 전송결과		*/
		TYPE_REPORT_ACK,	/* 전송결과Ack	*/
		TYPE_CONTROL,		/* CONTROL		*/
		TYPE_CONTROL_ACK	/* CONTROLAck	*/
	};

   CPacketCtrlSKB() {};
	virtual ~CPacketCtrlSKB() {};

	void SetFillUpSpace(char* pSource, int nSourceSize);
	void SetLeftFillUpZero(char* pSource, int nSourceSize);
	void SetLeftEmptyZero(char* pSource, int nSourceSize);
	void SetReplaceTel(char* pSource, int nSourceSize);

	//Packet Msg 생성
	int getMsg_AUTH_BIND_REQ(char* pBuff, char* id, char* aid, char* pw, unsigned long tid);
	int getMsg_BIND_REQ(char* pBuff, char* id, char* aid, char* pw, unsigned long tid);
	int getMsg_PING_REQ(char* pBuff, unsigned long tid);
#ifdef _URL_MODE
	int getMsg_DELIVER_REQ(char* pBuff, int nMsgSeq, char* base64msg, char* urlmsgdata, vector<string>& vtSend, unsigned long tid);
#else
	int getMsg_DELIVER_REQ(char* pBuff, int nMsgSeq, char* base64msg, vector<string>& vtSend, unsigned long tid);
#endif	
	int getMsg_REPORT_ACK(char* pBuff, char* sJobId, unsigned long tid);
	int getMsg_CONTROL_ACK(char* pBuff, unsigned long tid);
	int getMsg_PONG_RES(char* pBuff, unsigned long tid);
	//

	//Ack 받은 데이터 입력
	int getData_AuthBndAck(char* pBuff, vector<string>& vtBndAck);
	int getData_BndAck(char* pBuff, vector<string>& vtBndAck);
	int getData_SndAck(char* pBuff, vector<string>& vtSndAck);
	//

	//Server(SKB) -> Client(KSKYB) 데이터 입력
	int getData_Report(char* pBuff, vector<string>& vtReport);
	int getData_Control(char* pBuff, vector<string>& vtControl);
	//

	int getMsgCode(char* pBuff);
};

#endif /* PacketCtrlSKB_H_ */
