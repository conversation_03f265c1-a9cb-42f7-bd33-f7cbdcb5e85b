/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <string>
#include <vector>
#include <ml_ctrlsub.h>

using namespace std;

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(sql_context ctx, vector<string>& vtSndAck);
	//int setReportData(sql_context ctx, vector<string>& vtReport);
	int setReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtReport, char *Type);
	//int getSendData(sql_context ctx, int nQID, vector<string>& vtSend);
	long long getSendData(sql_context ctx, int nQID, vector<string>& vtSend);
	int _putMsgRetryDB(long long msgId, int telcoId, int priority, sql_context ctx);

private:
	bool m_bThread;
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
