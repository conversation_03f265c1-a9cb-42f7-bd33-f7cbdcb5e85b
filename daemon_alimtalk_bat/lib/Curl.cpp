#include "Curl.h"

void CCurl::init()
{
	//프로세스에서 한번말 실행해야 됨.스레드 생성전
	//curl_global_init(CURL_GLOBAL_DEFAULT);
	handle = curl_easy_init();
	headers = NULL; //꼭해줘라
}

void CCurl::setOptPost(char *targetUrl, string parameter)
{
	curl_easy_setopt(handle, CURLOPT_URL, targetUrl);
	curl_easy_setopt(handle, CURLOPT_HTTPHEADER, headers);
	//curl_easy_setopt(handle, CURLOPT_NOPROGRESS, 1);
	//curl_easy_setopt(handle, CURLOPT_VERBOSE, 1);
	curl_easy_setopt(handle, CURLOPT_WRITEFUNCTION, writer);
	curl_easy_setopt(handle, CURLOPT_WRITEDATA, &response);
	//curl_easy_setopt(handle, CURLOPT_READDATA, stdout);
	//curl_easy_setopt(handle, CURLOPT_READFUNCTION, writer);
	
	//SSL_VERIFY 옵션 하지 않으면 memory leak 발생!!
	curl_easy_setopt(handle, CURLOPT_SSL_VERIFYPEER, 0L);
	curl_easy_setopt(handle, CURLOPT_SSL_VERIFYHOST, 0L);	

	//시그널로 프로세스 죽는 문제 발생 하여 아래 옵션 추가
	curl_easy_setopt(handle, CURLOPT_NOSIGNAL, 1);
	
	curl_easy_setopt(handle, CURLOPT_POSTFIELDS, parameter.c_str());
	curl_easy_setopt(handle, CURLOPT_POSTFIELDSIZE, parameter.size());
	curl_easy_setopt(handle, CURLOPT_POST, 1);
	//curl_easy_setopt(handle, CURLOPT_TIMEOUT, 1);
	curl_easy_setopt(handle, CURLOPT_CONNECTTIMEOUT, 10);
}

void CCurl::setHeaderPost(char *string)
{
	headers = curl_slist_append(headers, string);
}

CURLcode CCurl::perform()
{
	return curl_easy_perform(handle);
}

void CCurl::cleanAll()
{
	curl_slist_free_all(headers);
	curl_easy_cleanup(handle);
	response.clear();
	headers = NULL;
	handle = NULL;
	//curl_global_cleanup();
}

CURLcode CCurl::getContentInfo(char *contentInfo)
{
	return curl_easy_getinfo(handle, CURLINFO_CONTENT_TYPE, &contentInfo);
}

/*
int CCurl::writer(char *data, size_t size, size_t nmemb, string *buffer_in)
{
	if(buffer_in != NULL)
	{
		buffer_in->append(data, size * nmemb);

		response = *buffer_in;

		return size * nmemb;
	}

	return 0;
}
*/
