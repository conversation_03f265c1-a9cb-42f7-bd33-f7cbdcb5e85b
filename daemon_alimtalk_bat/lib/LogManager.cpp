#include "LogManager.h"

#include <iostream>
using namespace std;
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <sys/time.h>

#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ether.h>
#include <net/ethernet.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <ifaddrs.h>
#include <vector>

#define MAX_LOG_LEN 4096-1

namespace
{
	const char ConvertToHex( const char cSource ) 
	{    
		return "0123456789abcdef"[ 0x0f & cSource ]; 
	} 

	const std::string URLEncoding( const std::string& kInput ) 
	{ 
		std::string kOutput; 

		std::string::const_iterator string_iter = kInput.begin(); 
		while( string_iter != kInput.end() ) 
		{ 
			const std::string::value_type element = (*string_iter); 
			if(*string_iter == '/' || *string_iter == '.')
			{
				kOutput += element;
			}else if( isascii( element ) // ASCII 문자 이고 
				&& isalnum( element ) ) // 대소문자, 그리고 숫자 
			{ 
				kOutput += element;
			} 
			else // 그 외 문자는 모두 %16진수 형태로 변환 
			{ 
				kOutput += "%"; 
				kOutput += ConvertToHex( element >> 4 ); 
				kOutput += ConvertToHex( element ); 
			} 

			++string_iter; 
		} 
		return kOutput; 
	}
}

LogManager::LogManager(void)
{	
	consol_ = true;
	alert_.alert	= 0;
	ProcessLog = NULL;
	MoniterLog = NULL;
	alert_.alertAddr = "10.216.243.231";
	alert_.alertPort = "80";
	alert_.alertPage = "/alertcall/alertcall.php";
	process_name_="";
}

LogManager::~LogManager(void)
{
}

LogManager& LogManager::GetInstance()
{
	static LogManager inst;
	return inst;
}

void LogManager::_write_console( const char* buf )
{
	if (!consol_)
		return;

	cout << buf;
	cout << endl;
}

int LogManager::Write(log_type t, const char *format, ... )
{
	va_list args;
	char logMsg[MAX_LOG_LEN];
	char tmpMsg[MAX_LOG_LEN];
	memset(logMsg, 0, MAX_LOG_LEN);
	memset(tmpMsg, 0, MAX_LOG_LEN);

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;

	time(&the_time);
	tm_ptr = localtime(&val.tv_sec);
	gettimeofday(&val,NULL);

	time(NULL);
	tm_ptr = localtime(&the_time);

	gettimeofday(&val,NULL);

	std::string mark="";
	switch(t)
	{

		case log_debug:{mark = "[DBUG]"; break;}
		case log_trace:{mark = "[TRC ]"; break;}
		case log_info:{mark = "[INFO]"; break;}
		case log_warning:{mark = "[WARN]"; break;}
		case log_session:{mark = "[SESS]"; break;}
		case log_error:{mark = "[ERR ]"; break;}
		default:{mark="[NONE]";}
	}

	snprintf(logMsg, sizeof(logMsg), "[%02d:%02d:%02d:%03d][%s:%s]:%s"
		,tm_ptr->tm_hour
		,tm_ptr->tm_min
		,tm_ptr->tm_sec
		,val.tv_usec/1000
		,process_name_.c_str()
		,mark.c_str()
		,tmpMsg);

	if((int)level_ <= (int)t)
	{
		if(ProcessLog)
			ProcessLog(logMsg);
	}

	if((int)log_error <= (int)t)
	{
		if(MoniterLog)
			MoniterLog(logMsg);

		if(alert_.alert == 1)
			_Alert(tmpMsg);
	}

	if(consol_)
		_write_console(logMsg);

	return 0;
}

void LogManager::SetProcessLog(void (*f)(std::string))
{
	ProcessLog = f;
}

void LogManager::SetMoniterLog(void (*f)(std::string))
{
	MoniterLog = f;
}

int LogManager::_strReplace(char* sourceMsg , char* a , char* b)
{
	int cnt;
	char* pstrtmp;
	char msgtmp[512];
	pstrtmp = (char*)strstr(sourceMsg,a);
	while(pstrtmp)
	{
		memset(msgtmp,0x00,sizeof(msgtmp));
		memcpy(msgtmp,sourceMsg,(cnt = (int)(pstrtmp - sourceMsg)));
		memcpy(msgtmp+cnt,b,strlen(b));
		memcpy(msgtmp+cnt+strlen(b),pstrtmp+strlen(a),strlen(pstrtmp+strlen(a)));		
		strcpy(sourceMsg,msgtmp);
		pstrtmp = (char*)strstr(pstrtmp+strlen(b),a);
	}
	return 1;
}


int LogManager::_Alert(char* pForm,...)
{
	int sockfd, len;
	struct sockaddr_in serv_addr;
	struct timeval tv;
	
	char strParam[256]={0,};
	char transBuf[350]={0,};

	va_list pArg;
	va_start(pArg, pForm);
	vsprintf(strParam, pForm, pArg);
	va_end(pArg);

	std::string msg;
	sprintf(transBuf,"[%s]\n[%s]\n[%s]", _get_ip().c_str(), process_name_.c_str(), strParam);
	
	msg = URLEncoding(transBuf);
// 	_strReplace(transBuf," ","%20");
// 
// 	msg = transBuf;
	memset(transBuf,0x00,sizeof(transBuf));
	sprintf(transBuf,"get %s?type=1&callType=%s&msgbody=%s\n",alert_.alertPage.c_str(), alert_.callType.c_str(), msg.c_str());	
	memset((char*)&serv_addr,0x00,sizeof(serv_addr));
	serv_addr.sin_family = AF_INET;
	serv_addr.sin_addr.s_addr = inet_addr(alert_.alertAddr.c_str());
	serv_addr.sin_port = htons(atoi(alert_.alertPort.c_str()));

	if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
		return -1;
	}
	tv.tv_sec = 5;
	tv.tv_usec = 0;

	if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
		return -1;
	}

	if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
		return -1;
	}

	if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
		return -1;
	}

	len = write(sockfd,transBuf,strlen(transBuf));
	if( len < 0 )
		return -1;

	memset(transBuf,0x080,sizeof(transBuf));
	len = read(sockfd,transBuf,sizeof(transBuf));
	close(sockfd);
	if (strncmp(transBuf,"OK",2) != 0 )
		return -1;

	return 0;
}

std::string LogManager::_get_ip()
{
// 	std::vector<std::string> ip4_list;
// 	std::vector<std::string> ip6_list;
	std::string ip="";
	struct ifaddrs* ifaddrStruct=NULL;
	struct ifaddrs* ifa=NULL;

	void* tmpAddrPtr=NULL;

	getifaddrs(&ifaddrStruct);

	for(ifa = ifaddrStruct; ifa != NULL ; ifa = ifa->ifa_next)
	{
		if(ifa->ifa_addr->sa_family==AF_INET)
		{
			tmpAddrPtr=&((struct sockaddr_in*)ifa->ifa_addr)->sin_addr;
			char addressBuffer[INET_ADDRSTRLEN]={0,};
			inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);

			if(strncmp(ifa->ifa_name, "eth0", 4) == 0)
				ip=addressBuffer;

		}else if(ifa->ifa_addr->sa_family==AF_INET6)
		{
			tmpAddrPtr=&((struct sockaddr_in6*)ifa->ifa_addr)->sin6_addr;
			char addressBuffer[INET6_ADDRSTRLEN]={0,};
			inet_ntop(AF_INET6, tmpAddrPtr, addressBuffer, INET6_ADDRSTRLEN);
// 			ip = ifa->ifa_name;
// 			ip6_list.push_back(ip);
		}
	}

	return ip;
}

