/*******************************************************
*
* File name: PollingReports.cc
* Author: <PERSON><PERSON><PERSON>
* Review: <PERSON><PERSON><PERSON><PERSON>
* To do: to get reports from <PERSON><PERSON><PERSON> alimtalk in polling method
*
*******************************************************/

//////////////////////////////////////////////////
// include
//////////////////////////////////////////////////
#include <iostream>
#include <map>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <time.h>
#include <sys/timeb.h>

#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/un.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"
#include "PacketCtrlSKB_MMS.h"
#include "DatabaseORA_MMS.h"

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

using namespace std;

//////////////////////////////////////////////////
// define
//////////////////////////////////////////////////
#define LOG_LENGTH            1024
#define MAX_POLLING_MESSAGES  1000
#define TMP_BUFF              1024
#define MMSID_LENGTH          16
#define DATE_LENGTH           8
//#define DEBUG

#define COMPLETE_AKC_FOR_KKO_RETRY_TIMES 3
#define COMPLETE_AKC_FOR_KKO_DELAY_TIME  1000

//////////////////////////////////////////////////
// Global variables
//////////////////////////////////////////////////
KSKYB::CProperties    g_prop;
KSKYB::CDatabaseORA   g_oracle;
ST_POLLING_SUCCESS    stSuccess[ MAX_POLLING_MESSAGES];
ST_POLLING_FAIL       stFail   [ MAX_POLLING_MESSAGES];
sql_context           ctx;
struct _message_info  message_info;
struct _shm_info *    shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[ 36];

int  activeProcess    = true;
char quid      [ 32]  = { 0x00,};
char targetUrl [ 128] = { 0x00,}; // 128 is fixed?
char requestUrl[ 128] = { 0x00,}; 
string channelKey; 
string channelYn; 

/*
typedef struct tag_resultTable {
    char tCode[8];
    char kCode[8];
    char desc[512];
} CODETABLE;
*/
int         nErrorCnt        = 0;     // Total number of errors
CODETABLE * ptErrorCodeTable = NULL;  // error table
//////////////////////////////////////////////////
// function declarations
//////////////////////////////////////////////////
int    init( int argc, char * argv[]);
int    getAllocResultTable( char * filename, CODETABLE ** ppstCodeTable);
int    sendCompleteAck( ST_TALK_POLLING_RES pollingResult);
void   Init_Server();
void   CloseProcess( int sig);
void   makeCurrentTime( string &cTime);
void   mnt( char * buf, int st, int err);
void   log( char * buf, int st, int err);
string getMMSId( string _mmsId);
string getReceivedTime( string _str);

//////////////////////////////////////////////////
// Methods
//////////////////////////////////////////////////
int main( int argc, char * argv[])
{
	//////////////////////////////////////////////////
	// Initialization
	char logMsg[ LOG_LENGTH];
	
	if(init( argc, argv) < 0) return -1;
	
	string startTime;
	string parameter ="";
	makeCurrentTime( startTime);
	sprintf(logMsg, "[INF] process started %s"\
	                , startTime.c_str());
	mnt(logMsg, 0, 0);
	
	CCurl curl;
	
	//////////////////////////////////////////////////
	// main thread
	while( activeProcess)
	{
		// period: 1s
		//sleep(1);
		//usleep(800000);
		
		CAlimtalkApi ata;
		
		if(channelYn.compare("Y") == 0)
		{	
			ata.makePollingRequestMsg(channelKey, parameter);
			
			curl.init();
			curl.setHeaderPost("Accept: application/json");
			curl.setHeaderPost("Content-type: application/json");
			curl.setOptPost( requestUrl, parameter);
		}else{
			curl.init();
			curl.setOptPost( requestUrl, "");
		}
			
		//////////////////////////////////////////////////
		// prepare and tranmit curl command  
		curl.response.clear();
		CURLcode rVal = curl.perform();
		
		sprintf(logMsg, "[INF] request report rVal[%d]", rVal);
			log(logMsg, 0, 0);
			
		//////////////////////////////////////////////////
		// get curl response and parsing JSON
		if( CURLE_OK == rVal)
		{
			sprintf( logMsg, "[INF] get curl response successfully");
			log( logMsg, 0, 0);
			
			ST_TALK_POLLING_RES pollingResult;
			int successSize = 0, failSize = 0;
			int ret = 0;

			//////////////////////////////////////////////////
			// parsing JSON
			ret = ata.parsingPollingMsgResponse( curl.response.c_str(), pollingResult\
                                         , stSuccess, stFail, successSize, failSize);

#ifdef DEBUG
			//sprintf(logMsg, "[DEB][full text]\r\n %s",curl.response.c_str());
			//log(logMsg, 0, 0);
			
			for(int i = 0; i < successSize; ++i)
			{
				sprintf(logMsg, "[DEB][success]sn:%s, status: %s, received_at: %s",
				        stSuccess[i].sn.c_str(), stSuccess[i].status.c_str(), stSuccess[i].received_at.c_str());
				log(logMsg, 0, 0);
			}
			for(int i = 0; i < failSize; ++i)
			{
				sprintf(logMsg, "[DEB][fail]sn:%s, status: %s, received_at: %s, message: %s",
				        stFail[i].sn.c_str(),          stFail[i].status.c_str(), 
				        stFail[i].received_at.c_str(), stFail[i].message.c_str());
				log(logMsg, 0, 0);
			}
#endif	                                         
			if(ret < 0) 
			{
				// Json parsing error
				sprintf(logMsg, "[ERR] response data parsing error.");
				log(logMsg, 0, 0);
			}
			else
			{
#ifdef DEBUG
				sprintf(logMsg, "[DEB][polling response]"\
				                "[code: %s][response_id: %s][responsed_at: %s]"\
				                "[message: %s]"\
				                 , pollingResult.code.c_str(), pollingResult.response_id.c_str()\
				                 , pollingResult.responsed_at.c_str() , pollingResult.message.c_str());
				log(logMsg, 0, 0);
#endif
				//////////////////////////////////////////////////
				// Detect errors
				if(pollingResult.code.compare( "0000") != 0)
				{
					sprintf( logMsg, "[ERR] Polling response error[code: %s][Msg: %s]"\
					                 , ( pollingResult.code).c_str(), ( pollingResult.message).c_str());
					log( logMsg, 0, 0);
				}
				else if(pollingResult.response_id.empty() == true)
				{
					sprintf( logMsg, "[INF] No report");
					log( logMsg, 0, 0);
					sleep(1);
				}
				else
				{
					sprintf(logMsg, "[INF] processing report data. response id: %s, succ: %d, fail: %d"\
					                 , ( pollingResult.response_id).c_str(), successSize, failSize);
					log(logMsg, 0, 0);
					//////////////////////////////////////////////////
					// data processing
					volatile static int succSize  = 0;
					succSize = successSize;
					volatile static int fail_Size = 0;
					fail_Size = failSize;
#ifdef DEBUG
					sprintf(logMsg, "[DEB][success size: %d][fail size: %d][succSize: %d][fail_Size: %d]", successSize, failSize, succSize, fail_Size);
					log(logMsg, 0, 0);
#endif 
					//////////////////////////////////////////////////
					// success
					if(succSize > 0)
					{
#ifdef DEBUG
					sprintf(logMsg, "[DEB][success]");
					log(logMsg, 0, 0);
#endif 
						char msg_id[succSize][50+1];
						//char cmms_id[succSize][32+1];
						char dlv_date[succSize][14+1];
						char res_code[succSize][4+1];
						char res_text[succSize][200+1];
						char end_telco[succSize][5+1];
						
						for(int i = 0; i < succSize; ++i)
						{
							//----- initialization
							memset(msg_id[i],    0x00, sizeof(msg_id[i]));
							memset(dlv_date[i],  0x00, sizeof(dlv_date[i]));
							memset(end_telco[i], 0x00, sizeof(end_telco[i]));
							memset(res_code[i],  0x00, sizeof(res_code[i]));
							memset(res_text[i],  0x00, sizeof(res_text[i]));
							//----- preprocessing data
							snprintf(msg_id[i],    sizeof(msg_id[i]),    "%s", getMMSId(stSuccess[i].sn).c_str());
							snprintf(dlv_date[i],  sizeof(dlv_date[i]),  "%s", getReceivedTime(stSuccess[i].received_at).c_str());
							snprintf(end_telco[i], sizeof(end_telco[i]), "%s", "KKO");
							
							for(int j = 0; j < nErrorCnt; ++j)
							{
								if(strcmp(ptErrorCodeTable[j].tCode, stSuccess[i].status.c_str()) == 0)
								{
									snprintf(res_code[i], sizeof(res_code[i]), "%s", ptErrorCodeTable[j].kCode);
									snprintf(res_text[i], sizeof(res_text[i]), "%s", ptErrorCodeTable[j].desc);
								}
							}
							if(strlen(res_code[i]) == 0)
							{
								snprintf(res_code[i], sizeof(res_code[i]), "%s", "8013");
								snprintf(res_text[i], sizeof(res_text[i]), "%s", "UNKNOWN");
							}

							sprintf(logMsg, "[INF][success] msg_id[%s] dlv_date[%s] res_code[%s] res_text[%s]",
							        msg_id[i], dlv_date[i], res_code[i], res_text[i]);
							log(logMsg, 0, 0);
						} // for(int i = 0; succSize > i; ++i)
						
						//////////////////////////////////////////////////
						// insert db
						int result = 0;
						result = g_oracle.setPollingReport_batch((int)atoi(quid), ctx, succSize, 
						                                         msg_id, dlv_date, res_code, 
						                                         res_text, end_telco);
						                                         
						if(result < 0)
						{
							// DB error
							sprintf(logMsg, "[ERR][success] [response_id: %s] DB insert fail. Polling reports",( pollingResult.response_id).c_str());
							log(logMsg, 0, 0);
							sleep(1);
							continue;
						} 
					} // if(0 != succSize)
					
#ifdef DEBUG
					sprintf(logMsg, "[DEB][fail][size: %d]"\
					                 , failSize);
					log(logMsg, 0, 0);
#endif
					//////////////////////////////////////////////////
					// fail
					if(fail_Size > 0)
					{
#ifdef DEBUG
						sprintf(logMsg, "[DEB][fail]");
						log(logMsg, 0, 0);
#endif 
						char msg_id_f[fail_Size][50+1];
						//char cmms_id_f[fail_Size][32+1];
						char dlv_date_f[fail_Size][14+1];
						char res_code_f[fail_Size][4+1];
						char res_text_f[fail_Size][200+1];
						char end_telco_f[fail_Size][5+1];
					
						for(int i = 0; i < fail_Size; ++i)
						{
							//----- initialization
							memset(msg_id_f[i],    0x00, sizeof(msg_id_f[i]));
							memset(dlv_date_f[i],  0x00, sizeof(dlv_date_f[i]));
							memset(end_telco_f[i], 0x00, sizeof(end_telco_f[i]));
							
							memset(res_code_f[i],  0x00, sizeof(res_code_f[i]));
							memset(res_text_f[i],  0x00, sizeof(res_text_f[i]));
							//----- preprocessing data
							snprintf(msg_id_f[i],    sizeof(msg_id_f[i]),    "%s", getMMSId(stFail[i].sn).c_str());
							snprintf(dlv_date_f[i],  sizeof(dlv_date_f[i]),  "%s", getReceivedTime(stFail[i].received_at).c_str());
							snprintf(end_telco_f[i], sizeof(end_telco_f[i]), "%s", "KKO");

							for(int j = 0; j < nErrorCnt ; ++j)
							{
								if(strcmp(ptErrorCodeTable[j].tCode, stFail[i].status.c_str()) == 0)
								{
									snprintf(res_code_f[i], sizeof(res_code_f[i]), "%s", ptErrorCodeTable[j].kCode);
									snprintf(res_text_f[i], sizeof(res_text_f[i]), "%s", ptErrorCodeTable[j].desc);
								}
							}
							
							if(strlen(res_code_f[i]) == 0)
							{
								snprintf(res_code_f[i], sizeof(res_code_f[i]), "%s", "8013");
								snprintf(res_text_f[i], sizeof(res_text_f[i]), "%s", "UNKNOWN");
							}
	
							sprintf(logMsg, "[INF][fail] msg_id[%s] dlv_date[%s] res_code[%s] res_text[%s]",
							        msg_id_f[i], dlv_date_f[i], res_code_f[i], res_text_f[i]);
							log(logMsg, 0, 0);
						} //for(int i = 0; fail_Size > i; ++i)
	
						//////////////////////////////////////////////////
						// insert db
						int result = 0;
						result = g_oracle.setPollingReport_batch((int)atoi(quid), ctx, fail_Size, 
						                                         msg_id_f, dlv_date_f, res_code_f, 
						                                         res_text_f, end_telco_f);
						                                         
						if(result < 0)
						{
							// DB error
							sprintf(logMsg, "[ERR][fail] [response_id: %s] DB insert fail. Polling reports",( pollingResult.response_id).c_str());
							log(logMsg, 0, 0);
							sleep(1);
							continue;
						} 
					} // if(0 != fail_Size)
						
					//////////////////////////////////////////////////
					// send report ack. It must be called after finished handling data.
					// if target url is "https://alimtalk-api.kakao.com/v2/fc8133d22a0b0f1e6fe4bb5616dec44dd987d301"
					//sendCompleteAck( pollingResult);
					for(int p = 0; p < COMPLETE_AKC_FOR_KKO_RETRY_TIMES ; ++p)
					{
						if(sendCompleteAck(pollingResult) < 0)
						{
#ifdef DEBUG
							sprintf(logMsg, "[DEB]retry: %d", p+1);
							log(logMsg, 0, 0);
#endif
							continue;
						}
						else
							break;
					}
				}
			} // if( 0 > ret): Json parsing
		}
		else
		{
			// curl error
			sprintf( logMsg, "[ERR] curl error: request report data");
			log( logMsg, 0, 0);
		} // if(CURLE_OK == rVal)
		// release curl resource
		curl.cleanAll();
	} // while(activeProcess)

	//////////////////////////////////////////////////
	// terminating process & release resources
	if( ptErrorCodeTable != NULL)
	{
		free(ptErrorCodeTable);
		ptErrorCodeTable = NULL;
	}
	// release curl resource
	//curl.cleanAll();
	curl_global_cleanup();

	string terminatedTime;
	makeCurrentTime( terminatedTime);
	sprintf( logMsg, "[INF] process terminated %s"\
	        , terminatedTime.c_str());
	mnt( logMsg, 0, 0);

	return 0;
} // main();

void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP,  CloseProcess);
	signal(SIGCLD,  SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess); //15
	signal(SIGINT,  CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess); //10
}

void CloseProcess( int sig)
{
	activeProcess = false;

	char logMsg[ 256];
	sprintf( logMsg, "[INF]CloseProcess Start & Exit [SIG:%d]", sig);
	mnt( logMsg, 0, 0);
}

int init( int argc, char * argv[])
{
	char logMsg[ LOG_LENGTH];
	g_prop.load(argv[1]);

	sprintf( quid,       "%s", g_prop.getProperty( "gw.quid"));
	sprintf( targetUrl,  "%s", g_prop.getProperty( "gw.target_url_polling"));
	sprintf( requestUrl, "%s", g_prop.getProperty( "gw.target_url_polling"));
	strcat(  requestUrl, "/responseAll");
	
	channelKey = g_prop.getProperty( "gw.polling_channelkey");
	channelYn = g_prop.getProperty( "gw.polling_channel_yn");
	
	if(channelYn.empty())
	{
		channelYn = "N";
	}
	else if(channelYn.compare("Y") != 0 )
	{
		channelYn = "N";
	}
	

	Init_Server();

	if( ml_sub_init( PROCESS_NO, PROCESS_NAME, ( char *)0, ( char *)0) < 0) 
	{
		sprintf(logMsg, "[PollingReports.cpp][ERR] ml_sub_init ERROR.");
		mnt(logMsg, 0, 0);
		return 0;
	}
	// oracle
	if( g_oracle.setEnableThreads() < 0)
	{
		sprintf(logMsg, "[PollingReports.cpp][ERR] g_oracle.setEnableThreads ERROR. process return;");
		mnt(logMsg, 0, 0);
		return 0;
	}
	
	if( g_oracle.initThread( ctx) < 0 || ctx == NULL)
	{
		sprintf( logMsg, "[PollingReports.cpp][ERR] g_oracle.initThread Fail.");
		mnt( logMsg, 0, 0);
		return -1;
	}

	if( g_oracle.connectToOracle( ctx, g_prop.getProperty( "db.uid")\
		                            , g_prop.getProperty( "db.dsn")) < 0)
	{
		sprintf( logMsg, "[PollingReports.cpp][ERR] g_oracle.connectToOracle() Fail.");
		mnt( logMsg, 0, 0);
		return -1;
	}
	// curl
	curl_global_init( CURL_GLOBAL_DEFAULT);
	//////////////////////////////////////////////////
	// get error codes
	nErrorCnt = getAllocResultTable( g_prop.getProperty( "code.reportCodeFile"), &ptErrorCodeTable);
	if( 0 >= nErrorCnt)
	{
		sprintf( logMsg, "[PollingReports.cpp][ERR]getAllocResultTable Fail.");
		mnt( logMsg, 0, 0);
		return -1;
	}
	
	return 0;
}

string getMMSId( string _mmsId)
{
	char logMsg[ LOG_LENGTH];

	string InsertedMMSId      = _mmsId;
 	char date[  DATE_LENGTH]  = { 0x00, };
	char mmsId[ MMSID_LENGTH] = { 0x00, };
	int result;	
	result = sscanf( InsertedMMSId.c_str(), "%8s-%s", date, mmsId);
	
#ifdef DEBUG
	sprintf( logMsg, "[PollingReports.cpp][DEB][sn: %s][date: %s][mms_id: %s]"\
	                 , InsertedMMSId.c_str() ,date, mmsId);
	log( logMsg, 0, 0);
#endif

	if( 0 > result)
	{
		// error
		sprintf( logMsg, "[PollingReports.cpp][ERR] MMS id extracting error.");
		log( logMsg, 0, 0);
		return NULL;
	}

	return mmsId;
}

string getReceivedTime( string _recievedTime)
{
	string recievedTime = _recievedTime;
	CAlimtalkApi ata;

	string report_dt;
	if( 0 < recievedTime.size())
	{
		ata.makeDateString( recievedTime, report_dt);
	}
	else //시간 정보가 없으면 현재시간으로 set
	{
		string cTime;
		makeCurrentTime( cTime);
		report_dt = cTime;
	}
	return report_dt;
}

int sendCompleteAck( ST_TALK_POLLING_RES pollingResult)
{
	char logMsg[ LOG_LENGTH];
	memset( logMsg, 0x00, LOG_LENGTH);
	
	char requestAckUrl[ 256] = { 0x00,}; 
	sprintf( requestAckUrl, "%s", targetUrl);
	strcat(  requestAckUrl, "/response/");
	strcat(  requestAckUrl, ( const char*)( pollingResult.response_id).c_str());
	strcat(  requestAckUrl, "/complete");
	
#ifdef DEBUG
		sprintf(logMsg, "[PollingReports.cpp][DEB] complete ack url: %s"
		                 , requestAckUrl);
		log(logMsg, 0, 0);
#endif
	
	CCurl curl;
	curl.init();
	curl.setOptPost( requestAckUrl, "");
	curl.response.clear();
	
	CURLcode curlRes = curl.perform();
	
	sprintf(logMsg, "[INF] complete ack curlRes[%d]", curlRes);
			log(logMsg, 0, 0);
			
	if( CURLE_OK == curlRes)
	{
		// process Json
		Json::Value  root;
		Json::Reader reader;
	
		bool parsingSuccessful = reader.parse( curl.response.c_str(), root);
		// release curl resource
		curl.cleanAll();
		
		if( !parsingSuccessful)
		{
			sprintf( logMsg, "[PollingReports.cpp][ERR] Json parsing error(Request complete).");
			log( logMsg, 0, 0);
			return -1;
		}	
	
		if( 0 != root[ "code"].asString().compare( "0000") )
		{
			sprintf( logMsg, "[PollingReports.cpp][ERR] Error complete [code: %s][msg: %s]."\
			         , root[ "code"].asString().c_str(), root[ "message"].asString().c_str());
			log( logMsg, 0, 0);

			return -1;
		}

		// success
		sprintf(logMsg, "[PollingReports.cpp][INF] Curl complete response success. response id: %s"
		                 , ( pollingResult.response_id).c_str());
		log(logMsg, 0, 0);
	}
	else
	{
		sprintf( logMsg, "[PollingReports.cpp][ERR] Curl complete response fail. response id: %s"
		                 , ( pollingResult.response_id).c_str());
		log( logMsg, 0, 0);
		
		// release curl resource
		curl.cleanAll();
		
		return -1;
	}
	
	return 0;
}

int getAllocResultTable(char * filename, CODETABLE ** ppstCodeTable)
{
    FILE * fp = NULL;
    char buff[ TMP_BUFF];
    int cnt = 0;
    char * szTmp;

    fp = fopen( filename, "rt");
    if( NULL ==  fp)
    {
        printf( "ERROR [%s]\n", strerror( errno));
        return -1;
    }
// count
    memset( buff, 0x00, (sizeof( char) * TMP_BUFF));
    while( fgets( buff, (sizeof(char) * TMP_BUFF), fp))
    {
        if('=' == buff[0])
            cnt++;
    }

    *ppstCodeTable = ( CODETABLE*)malloc( sizeof( CODETABLE)*cnt);
    memset( *ppstCodeTable, 0x00, (sizeof( CODETABLE) * cnt));

    fseek( fp, 0L, SEEK_SET);
    cnt = 0;
    memset( buff, 0x00, (sizeof(char) * TMP_BUFF));
// set data
    while( fgets( buff, (sizeof(char) * TMP_BUFF), fp))
    {
        if(buff[0] != '=')
            continue;

        szTmp = (char*)strtok(buff," =");
        if( szTmp == NULL)
            continue;

        memcpy((*ppstCodeTable)[cnt].tCode, szTmp,strlen(szTmp));
        szTmp = (char*)strtok(NULL," =");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].kCode, szTmp,strlen(szTmp));

        szTmp = (char*)strtok(NULL,"=");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].desc, szTmp,strlen(szTmp));
        cnt++;
    }
    fclose(fp);

    return cnt;
}

void makeCurrentTime( string &cTime)
{
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};
	time( &tm_time);
	st_time = localtime(&tm_time);
	strftime( buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
}

void log( char * buf, int st, int err)
{
	char log[ 1024] = { 0x00,};
	snprintf( log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if( ml_sub_send_log( ( char *)log, sizeof( log), 3, st, err) <= 0) {
		printf( "%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}	
}

void mnt( char * buf, int st, int err)
{
	char log[ 1024] = { 0x00,};
	snprintf( log, sizeof( log)-1, "[%s]%s", PROCESS_NAME, buf);

	if( ml_sub_send_moni( ( char *)log, sizeof( log), 3, st, err) <= 0) {
		printf( "%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}


/* End of file */
