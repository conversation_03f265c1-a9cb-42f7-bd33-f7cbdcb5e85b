//============================================================================
//============================================================================
// Name        : telco_skb_new.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

#include <iostream>
#include <map>
using namespace std;

#include <sys/timeb.h>

#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include <unistd.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <signal.h>
#include <arpa/inet.h>
#include <sys/un.h>

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"
#include "PacketCtrlSKB_MMS.h"
#include "DatabaseORA_MMS.h"
#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

// 최대 쓰레드 POOL 크기
#define MAX_THREAD_POOL 128
using namespace std;

struct _message_info message_info;
struct _shm_info *shm_info;

typedef struct _basicInfo
{
	pthread_t tid;
	int inum;
	char quid[32];
	char qname[32];
	char target_url[128];
}basicInfo;

// 전역 쓰레드 구조체 
typedef struct _ph
{
	int data;    // 현재 사용중인 소켓 fd
	int index_num;// 인덱스 번호
}ph;

// 전역쓰레드 구조체로써 
// 현재 쓰레드 상황을 파악함
struct schedul_info
{
	map<string,string> mapSendMsg;// 가장최근에 만들어진 소켓지시자
	bool setFlag;
	multimap<int, ph> phinfo;
};

// 각 쓰레드별 조건변수
pthread_cond_t *mycond;
// 쓰레드 동기화를 위한 조건변수
pthread_cond_t async_cond = PTHREAD_COND_INITIALIZER;

// 각 쓰레드별 조건변수의 크리티컬세션 지정을 위한 
// 뮤텍스 
pthread_mutex_t mutex_lock= PTHREAD_MUTEX_INITIALIZER; 

pthread_mutex_t mutex_db_lock= PTHREAD_MUTEX_INITIALIZER; 
// 쓰레드 동기화용 조건변수의 크리티컬세션 지정을 위한 
// 뮤텍스
pthread_mutex_t async_mutex = PTHREAD_MUTEX_INITIALIZER;

schedul_info s_info;

sql_context ctx;

KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;
int activeProcess = true;
char PROCESS_NO[7], PROCESS_NAME[36];
void* procSendRept(void* param);
int CheckThreadStatus(basicInfo** param, int nCnt);
void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);
void makeCurrentTime(string &cTime);
int nThreadCnt = 0;

int main(int argc, char* argv[])
{
	
	int idx;
	char logMsg[512];
	g_prop.load(argv[1]);

	nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	int nServer = g_prop.getPropertyInt("gw.server");

	char quid[32] = {0x00,};
	sprintf(quid,"%s",g_prop.getProperty("gw.quid"));

	char qname[32] = {0x00,};
	sprintf(qname,"%s",g_prop.getProperty("gw.qname"));

	char targetUrl[128] = {0x00,};
	sprintf(targetUrl, "%s", g_prop.getProperty("gw.target_url"));

	Init_Server();

	printf("ALIMTALK THREAD CNT:[%d]\n", nThreadCnt);

	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) 
	{
		printf("ml_sub_init ERROR.\n");
		return 0;
	}

	int i;
	ph myph;
	int status;

	if (g_oracle.setEnableThreads()<0)
	{
		sprintf(logMsg, "[%s():%d][setEnableThreads ERROR. process return;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);

		return 0;
	}
	
	if (g_oracle.initThread(ctx) < 0 || ctx == NULL)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.initThread Fail.]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	if(g_oracle.connectToOracle(ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.connectToOracle() Fail..]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	curl_global_init(CURL_GLOBAL_DEFAULT);

	// 스레드 갯수만큼 조건변수 생성 
	mycond = (pthread_cond_t *)malloc(sizeof(pthread_cond_t)*nThreadCnt);

	// 스레드 전역변수 초기화
	s_info.mapSendMsg.clear(); 

	basicInfo tBasicInfo[nThreadCnt];

	// 스레드 POOL 생성
	for(i = 0; i < nThreadCnt; i++)
	{
		memset((void *)&myph, 0x00, sizeof(myph));
		myph.index_num = i;
		s_info.phinfo.insert(pair<int, ph>(0, myph));
	
		tBasicInfo[i].inum = i;
		snprintf(tBasicInfo[i].quid, sizeof(tBasicInfo[i].quid), quid); 
		snprintf(tBasicInfo[i].qname, sizeof(tBasicInfo[i].qname), qname); 
		snprintf(tBasicInfo[i].target_url, sizeof(tBasicInfo[i].target_url), targetUrl); 

		pthread_cond_init(&mycond[i], NULL);
		// 조건변수를 이용해서 쓰레드간 동기화를 실시한다.
		pthread_mutex_lock(&async_mutex);

		if(pthread_create(&tBasicInfo[i].tid, NULL, procSendRept, &tBasicInfo[i]) < 0)
		{
			perror("thread can't be created");
			exit(0);
		}

		pthread_cond_wait(&async_cond, &async_mutex); 
		pthread_mutex_unlock(&async_mutex);
	}

	//데이터 중복 가져가기 방지용 flag	
	s_info.setFlag = true;

	time_t now, last;
	double diff;
	time(&last);

	while(activeProcess)
	{
		//check threads alive
		time(&now);
		diff = difftime(now, last);
		if(diff > 120)
		{
			if(CheckThreadStatus((basicInfo**)tBasicInfo, nThreadCnt) < 0)
				break;

			time(&last);
			usleep(100000);
		}

		//0.02초마다 
		usleep(20000);
		map<string, string> mapSendMsg;
		
		long long msgid = 0;
		pthread_mutex_lock(&mutex_db_lock);
		msgid = g_oracle.getMsgData_V2(ctx, qname, mapSendMsg);
		pthread_mutex_unlock(&mutex_db_lock);

		if(msgid < 0)
		{
			activeProcess = false;
			sprintf(logMsg, "[%s:%d][msgid is -1(ERROR)activeProcess = false]", __FILE__, __LINE__);
			log(logMsg, 0, 0);
			break;
		}
		else if(msgid > 0)
		{	
			//sprintf(logMsg, "getMsg id[%lld]", msgid);
			//log(logMsg, 0, 0);

			while(true)
			{
				multimap<int, ph>::iterator mi;
				mi = s_info.phinfo.begin();

				if (mi->first == 1)
				{
					//sprintf(logMsg, "[no thread available]");
					//log(logMsg, 0, 0);

					//가용한 스레드가 없을 경우 0.1초 후 다시 시도
					usleep(100000);
					continue;
				}

				
				if(s_info.setFlag == false)
				{
					//cout << "data unavailable" << endl;
					continue;
				}

				ph tmpph;

				//data 중복 전송 방지 flag
				s_info.setFlag = false;
				s_info.mapSendMsg = mapSendMsg;

				tmpph.index_num = mi->second.index_num;
				s_info.phinfo.erase(mi);
				s_info.phinfo.insert(pair<int, ph>(1,tmpph));

				//pthread_mutex_lock(&mutex_lock);
				int ret = pthread_cond_signal(&mycond[mi->second.index_num]);
				//pthread_mutex_unlock(&mutex_lock);
				break;
			}
		}
		else
		{
			continue;
		}

		mapSendMsg.clear();
	}

	sprintf(logMsg, "[%s()][main Process End.]", __func__);
	mnt(logMsg, 0, 0);
	
	ml_sub_end();
	g_oracle.closeFromOracle(ctx);

	printf("alimtalk ml_sub_end\n");

	return 0;
}

int CheckThreadStatus(basicInfo** param, int nThreadCnt)
{
    char logMsg[256];
    int idx, status;
    KSKYB::CSocketTCP sockInst;
    basicInfo tpSub[nThreadCnt];
    memcpy(tpSub, param, sizeof(basicInfo) * nThreadCnt);

	sprintf(logMsg, "[%s()][thread check]", __func__);
	mnt(logMsg, 0, 0);

	for (idx = 0; idx < nThreadCnt; idx++) 
	{
		if (pthread_kill(tpSub[idx].tid, 0) != 0) 
		{
			pthread_join(tpSub[idx].tid, (void**)&status);
			activeProcess = false;
			sprintf(logMsg, "[%s()][CheckThreadStatus fail]", __func__);
			mnt(logMsg, 0, 0);
			return -1;
		}
	}

	return 0;
}

void* procSendRept(void* param)
{
	basicInfo bi;
	memcpy(&bi, param, sizeof(bi));
   	// 쓰레드 동기화용 조건변수
    pthread_mutex_lock(&async_mutex);
    pthread_cond_signal(&async_cond);
	pthread_mutex_unlock(&async_mutex);

	int mynum = bi.inum;
	char target_url[128] = {0x00,};
	char quid[32] = {0x00,};
	char qname[32] = {0x00,};
	char logMsg[1024] = {0x00,};

	snprintf(target_url, sizeof(target_url), bi.target_url);
	snprintf(quid, sizeof(quid), bi.quid);
	snprintf(qname, sizeof(qname), bi.qname);

	sprintf(logMsg, "procSendRept() [mynum:%d]Thread Start", mynum);
	log(logMsg, 0, 0);

	multimap<int, ph>::iterator mi;

	try {
		while(activeProcess) 
		{
			map<string,string> mapSendMsg;
			pthread_mutex_lock(&mutex_lock);
			pthread_cond_wait(&mycond[mynum], &mutex_lock);

			if(activeProcess == false)
			{
				break;
			}

			mapSendMsg = s_info.mapSendMsg;
			s_info.setFlag = true;

			pthread_mutex_unlock(&mutex_lock);

			long long mms_id = atoll(mapSendMsg["mms_id"].c_str());

			mi = s_info.phinfo.begin();
			map<string, string> mapReport;
			CCurl curl;

			while(mi != s_info.phinfo.end())
			{
				if(mi->second.index_num == bi.inum)
				{
					string parameter;
					CAlimtalkApi ata;
					ata.makeSmsRequestMsgBt(mapSendMsg, parameter, mms_id);
				
					//snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld]msg[%s]", bi.inum, mms_id, parameter.c_str());
					snprintf(logMsg, sizeof(logMsg), "tnum[%d]send[%lld]", bi.inum, mms_id);
					log(logMsg, 0, 0);
					curl.init();
					curl.setHeaderPost("Accept: application/json");
					curl.setHeaderPost("Content-type: application/json");
					curl.setOptPost(target_url, parameter);

					curl.response.clear();
					CURLcode rVal;
					rVal = curl.perform();
						
					sprintf(logMsg, "tnum[%d]mms_id[%lld]rVal[%d]", bi.inum, mms_id, rVal);
					log(logMsg, 0, 0);

					/*
					double tt = 0.0;
					double ns = 0.0;
					double ct = 0.0;
					double pt = 0.0;
					double st = 0.0;
					int curlRC = curl_easy_getinfo(curl.handle, CURLINFO_TOTAL_TIME, &tt);
					curlRC = curl_easy_getinfo(curl.handle, CURLINFO_NAMELOOKUP_TIME, &ns);
					curlRC = curl_easy_getinfo(curl.handle, CURLINFO_CONNECT_TIME, &ct);
					curlRC = curl_easy_getinfo(curl.handle, CURLINFO_PRETRANSFER_TIME, &pt);
					curlRC = curl_easy_getinfo(curl.handle, CURLINFO_STARTTRANSFER_TIME, &st);

					sprintf(logMsg, "total[%f]lookup[%f]connect[%f]pretrans[%f]starttransfer[%f]", tt, ns, ct, pt, st);
					log(logMsg, 0, 0);
					*/

					if(CURLE_OK == rVal)
					{
						snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld]res[%s]", bi.inum, mms_id, curl.response.c_str());
						log(logMsg, 0, 0);

						//response Json데이터 파싱
						ST_TALK_RES res;
						int ret = ata.parsingResponse(curl.response.c_str(), res);

						//Json 파싱 에러
						if(ret < 0)
						{
							sprintf(logMsg, "mms_id[%lld] response parsing error", mms_id);
							log(logMsg, 0, 0);
							char cmsg_id[32] = {0x00,};
							sprintf(cmsg_id, "%lld", mms_id);
							mapReport["msg_id"] = cmsg_id;
						
							string cTime;	
							makeCurrentTime(cTime);

							mapReport["dlv_date"] = cTime; 
							mapReport["res_code"] = "8001";
							mapReport["res_text"] = "fail";

							mapReport["end_telco"] = "KKO";
							mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
						}
						else 
						{
							char cmsg_id[32] = {0x00,};
							sprintf(cmsg_id, "%lld", mms_id);
							mapReport["msg_id"] = cmsg_id;

							//received_at 휴대폰이 메시지를 수신한 시간(읽은 시간 아님)
							if(res.received_at.size() > 0)
							{
								string report_dt;
								ata.makeDateString(res.received_at, report_dt);
								mapReport["dlv_date"] = report_dt;
							}
							else //시간 정보가 없으면 현재시간으로 set
							{
								string cTime;
								makeCurrentTime(cTime);
								mapReport["dlv_date"] = cTime;
							}

							if(res.code == "0000") 
							{
								mapReport["res_code"] = "1000";
								mapReport["res_text"] = "success";
							}
							else
							{
								mapReport["res_code"] = res.code;
								mapReport["res_text"] = res.message;
							}

							mapReport["end_telco"] = "KKO";
							mapReport["rcv_numb"] = mapSendMsg["dst_addr"];

						}
					
						break;
					}
					else
					{
						sprintf(logMsg, "no response msgid[%lld]", mms_id);
						log(logMsg, 0, 0);

						char cmsg_id[32] = {0x00,};
						sprintf(cmsg_id, "%lld", mms_id);
						mapReport["msg_id"] = cmsg_id;

						string cTime;
						makeCurrentTime(cTime);

						mapReport["dlv_date"] = cTime;
						mapReport["res_code"] = "9999";
						mapReport["res_text"] = "fail";

						mapReport["end_telco"] = "KKO";
						mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
						
						break;
					}
				}

				mi++;
			}

			pthread_mutex_lock(&mutex_db_lock);
			g_oracle.setReportData((int)atoi(quid),ctx, mapReport);
			pthread_mutex_unlock(&mutex_db_lock);

			mapSendMsg.clear();
			mapReport.clear();
			curl.cleanAll();

			ph tmpph;
			tmpph.index_num = mynum;
			s_info.phinfo.erase(mi);
			s_info.phinfo.insert(pair<int, ph>(0, tmpph));

		}
	}
	catch (myException* excp) {
		sprintf(logMsg, "[%s():%d][%s]", __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(logMsg, 0, 0);
		sprintf(logMsg, "[%s():%d][catch (myException* excp) return -1;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		delete excp;
	}
	catch (...) {
	}

	activeProcess = false;

	sprintf(logMsg, "procSendRept Thread[%d] End", mynum);
	mnt(logMsg, 0, 0);

	return NULL;
}

void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
}

void CloseProcess(int sig)
{
	activeProcess = false;
	/*
	for(int i = 0; i < nThreadCnt; i++)
	{
		int ret = pthread_cond_signal(&mycond[i]);
	}
	*/

	char logMsg[256];
	sprintf(logMsg,"CloseProcess Start & Exit [SIG:%d]\n", sig);
	mnt(logMsg, 0, 0);
	curl_global_cleanup();

}

void makeCurrentTime(string &cTime)
{
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
}

void log(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_log((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}	
}

void mnt(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_moni((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}
