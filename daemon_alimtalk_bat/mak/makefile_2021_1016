PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
CFLAGS = -g -Wall
CMMSFLAGS = -g -Wall -D_MMS_MODE

SMS_DBSTRING=neomms
SMS_DBID=bcreal
SMS_DBPASS=bcreal

MMS_DBSTRING=NEO226
MMS_DBID=neoatk1
MMS_DBPASS=neoatk1

ORG_D=${HOME}/daemon_alimtalk_bat
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=${HOME}/command_alimtalk_bat/obj/sms_ctrlsub++.o
EXT_INC=${HOME}/command_alimtalk_bat/inc

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC1 = ${ORACLE_HOME}/rdbms/public

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(ORA_INC) -I$(ORA_INC1) -I/usr/include/curl 
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC) -L$(ORA_INC1) -L/usr/lib64 
ORALIB = -lclntsh
LIBS = -lcurl -lpthread 

#all: alimtalk_mms alimtalk_bt_mms atalk_send_v2
all: atalk_send_v3 PollingReports

alimtalk_mms : $(OBJ_D)/alimtalk_mms.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/alimtalk_mms

$(OBJ_D)/alimtalk_mms.o: $(SRC_D)/alimtalk_main.cpp
	$(RM) -rf $(OBJ_D)/alimtalk_mms.*
	$(COPY) $(SRC_D)/alimtalk_main.cpp $(OBJ_D)/alimtalk_mms.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/alimtalk_mms.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) -o $(OBJ_D)/alimtalk_mms.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/alimtalk_mms.cpp

alimtalk_bt_mms : $(OBJ_D)/alimtalk_bt_mms.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/alimtalk_bt_mms

$(OBJ_D)/alimtalk_bt_mms.o: $(SRC_D)/alimtalk_bt_main.cpp
	$(RM) -rf $(OBJ_D)/alimtalk_bt_mms.*
	$(COPY) $(SRC_D)/alimtalk_bt_main.cpp $(OBJ_D)/alimtalk_bt_mms.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/alimtalk_bt_mms.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) -o $(OBJ_D)/alimtalk_bt_mms.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/alimtalk_bt_mms.cpp

atalk_send_v2 : $(OBJ_D)/atalk_send_v2.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/atalk_send_v2
	
atalk_send_v3 : $(OBJ_D)/atalk_send_v3.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/atalk_send_v3

atalk_send_v4 : $(OBJ_D)/atalk_send_v4.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/atalk_send_v4



PollingReports: $(OBJ_D)/PollingReports.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/PollingReports
	
$(OBJ_D)/atalk_send_v2.o: $(SRC_D)/atalk_send_v2.cpp
	$(RM) -rf $(OBJ_D)/atalk_send_v2.*
	$(COPY) $(SRC_D)/atalk_send_v2.cpp $(OBJ_D)/atalk_send_v2.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/atalk_send_v2.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) -o $(OBJ_D)/atalk_send_v2.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/atalk_send_v2.cpp
	
$(OBJ_D)/atalk_send_v3.o: $(SRC_D)/atalk_send_v3.cpp
	$(RM) -rf $(OBJ_D)/atalk_send_v3.*
	$(COPY) $(SRC_D)/atalk_send_v3.cpp $(OBJ_D)/atalk_send_v3.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/atalk_send_v3.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/atalk_send_v3.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/atalk_send_v3.cpp

$(OBJ_D)/atalk_send_v4.o: $(SRC_D)/atalk_send_v4.cpp
	$(RM) -rf $(OBJ_D)/atalk_send_v4.*
	$(COPY) $(SRC_D)/atalk_send_v4.cpp $(OBJ_D)/atalk_send_v4.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/atalk_send_v4.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/atalk_send_v4.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/atalk_send_v4.cpp


$(OBJ_D)/PollingReports.o: $(SRC_D)/PollingReports.cpp
	$(RM) -rf $(OBJ_D)/PollingReports.*
	$(COPY) $(SRC_D)/PollingReports.cpp $(OBJ_D)/PollingReports.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/PollingReports.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/PollingReports.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/PollingReports.cpp
	
$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
	$(RM) -rf $(OBJ_D)/SocketTCP.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlSKB.o: $(LIB_D)/PacketCtrlSKB.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKB.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlSKB_URL.o: $(LIB_D)/PacketCtrlSKB.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKB_URL.*
	$(CC) -o $@ $(CURLFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlSKB_MMS.o: $(LIB_D)/PacketCtrlSKB_MMS.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKB_MMS.*
	$(CC) -o $@ $(CFLAGS) $(CMMSFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/ksbase64.o: $(LIB_D)/ksbase64.cpp
	$(RM) -rf $(OBJ_D)/ksbase64.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA.o: $(LIB_D)/DatabaseORA.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA.*
	$(COPY) $(LIB_D)/DatabaseORA.cpp $(OBJ_D)/DatabaseORA.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(SMS_DBID)/$(SMS_DBPASS)@$(SMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA.o $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA.cpp

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) mode=oracle dbms=v7 unsafe_null=yes char_map=string iname=$(OBJ_D)/DatabaseORA_MMS.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) $(CMMSFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o $(CMMSFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/LogManager.o: $(LIB_D)/LogManager.cpp
	$(RM) -rf $(OBJ_D)/LogManager.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $(OBJ_D)/Curl.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/alimTalkApi.o: $(LIB_D)/alimTalkApi.cpp
	$(RM) -rf $(OBJ_D)/alimTalkApi.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $(OBJ_D)/jsoncpp.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/alimtalk_mms_tmp $(BIN_D)/alimtalk_mms
	mv $(BIN_D)/alimtalk_bt_mms_tmp $(BIN_D)/alimtalk_bt_mms
	rm tp*
