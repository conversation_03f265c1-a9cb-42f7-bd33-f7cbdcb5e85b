cmake_minimum_required(VERSION 3.16)
project(daemon_alimtalk_bat)

set(CMAKE_CXX_STANDARD 98)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall -std=gnu++98")

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Oracle Pro*C 컴파일러 찾기
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)

# Oracle Pro*C 전처리 함수 (THREADS 옵션을 조건부로 적용)
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # .cpp를 .pc로 복사
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # DatabaseORA_MMS만 THREADS=YES 사용, 나머지는 제외
    if(source_name STREQUAL "DatabaseORA_MMS")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # Oracle Pro*C 전처리
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_SOURCE_DIR}/../command_alimtalk_bat/inc
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            define=__sparc
            config=${PROC_CONFIG}
            SQLCHECK=SEMANTICS
            userid=neoatk/neoatk@NEO226
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# 인클루드 디렉토리 (실제 존재하는 경로만 사용)
include_directories(
    inc
    lib
    ${CMAKE_SOURCE_DIR}/../command_alimtalk_bat/inc
    ${PROC_INCLUDE}
    /usr/include/curl
)

# 라이브러리 디렉토리
link_directories(
    ${ORACLE_HOME}/lib
    /usr/lib64
)

# 컴파일 정의 (기본 정의만 유지)
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# Oracle 헤더 파일 강제 포함 제거 (Pro*C가 자동으로 처리)

# Oracle Pro*C 파일들에만 적용할 특별한 컴파일 옵션 (단순화)
set(ORACLE_COMPILE_FLAGS 
    "-D_MMS_MODE"
)

# 공통 라이브러리 (Oracle Pro*C 없는 파일들)
add_library(daemon_alimtalk_lib STATIC
    lib/Properties.cpp
    lib/myException.cpp
    lib/Curl.cpp
    lib/alimTalkApi.cpp
    lib/jsoncpp.cpp
)

# DatabaseORA_MMS (Oracle Pro*C 필요) - 특별한 컴파일 옵션 적용
add_library(database_ora_mms STATIC)
add_proc_source(database_ora_mms ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)

# Oracle Pro*C로 생성된 파일들에 특별한 컴파일 옵션 적용
target_compile_options(database_ora_mms PRIVATE ${ORACLE_COMPILE_FLAGS})

# 실행파일들 - 특별한 컴파일 옵션 적용
add_executable(atalk_send_v3)
add_proc_source(atalk_send_v3 ${CMAKE_CURRENT_SOURCE_DIR}/src/atalk_send_v3.cpp)
target_compile_options(atalk_send_v3 PRIVATE ${ORACLE_COMPILE_FLAGS})

add_executable(atalk_send_v4)
add_proc_source(atalk_send_v4 ${CMAKE_CURRENT_SOURCE_DIR}/src/atalk_send_v4.cpp)
target_compile_options(atalk_send_v4 PRIVATE ${ORACLE_COMPILE_FLAGS})

add_executable(PollingReports)
add_proc_source(PollingReports ${CMAKE_CURRENT_SOURCE_DIR}/src/PollingReports.cpp)
target_compile_options(PollingReports PRIVATE ${ORACLE_COMPILE_FLAGS})

# 링크 라이브러리 설정
foreach(target atalk_send_v3 atalk_send_v4 PollingReports)
    target_link_libraries(${target}
        daemon_alimtalk_lib
        database_ora_mms
        ${CMAKE_SOURCE_DIR}/../command_alimtalk_bat/obj/sms_ctrlsub++.o
        clntsh
        curl
        pthread
    )
endforeach()

# 출력 디렉토리 설정
set_target_properties(atalk_send_v3 atalk_send_v4 PollingReports PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
)
