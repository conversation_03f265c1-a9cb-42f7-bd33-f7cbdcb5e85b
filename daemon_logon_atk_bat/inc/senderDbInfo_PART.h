#ifndef _SENDER_DB_INFO_H_
#define _SENDER_DB_INFO_H_

#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "senderInfo.h"

enum {
    GETCTNID,
    GETMMSID,
    SETSENDQUE,
    SETSENDTBL,
    SETSENDCTNTBL,
    SETSENDQUE_FILTER
};

class CSenderDbInfo {
    public:
        TypeMsgDataSnd smsData;
        CLogonDbInfo logonDbInfo;
        char szReserve[128];
        CSenderInfo senderInfo;
        int result;
};

class CSenderDbInfoAck {
    public:
        int msgid;
        char szResult[4];
        int mmsid;
        int ctnid;
};

//class CSenderDbMMSID {
//    public:
//        int type; /* default : header */
//        char szCid[10+1];
//        int mmsid;
//        int ctnid;
//};
class Header {
    public:
        int type;
        int leng;
};



class CSenderDbMMSTBL {
    public:
        Header header;
//        int type; /* default : header */
        char szDstAddr[16+1];
        char sz<PERSON>allBack[16+1];
        char szMsgTitle[200+1];
        char szPtnSn[20+1];
        char szResvData[200+1];
        char szCid[10+1];
        int nMsgType;
        int nPriority;
        int nCtnId;
        int nCtnType;
        int nRgnRate;
        int nInterval;
        int nTextCnt;
        int nImgCnt;
        int nAugCnt;
        int nMpCnt;
        int nMMSId;
};

class CSenderDbMMSMSG {
    public:
        Header header;
//        int type; /* default : header */
        char szQName[32+1];
        int nPriority;
        int nCtnId;
        char szCallBack[16+1];
        char szDstAddr[16+1];
        char szMsgTitle[100+1];
        int nCntType;
        char szTxtPath[256+1];
        int nRgnRate;
        int nInterval;
        int nMMSId;
        char szResvData[200+1];
};


class CSenderDbMMSID {
    public:
        Header header;
//        int type; /* default : header */
        char szCid[10+1];
        int mmsid;
        int ctnid;
};

class CSenderDbMMSCTNTBL {
    public:
        Header header;
//        int type; /* default : header */
        int nCtnId;
        char szCtnName[50+1];
        char szCtnMime[50+1];
        int nCtnSeq;
        char szCtnSvc[5+1];
};



#endif

