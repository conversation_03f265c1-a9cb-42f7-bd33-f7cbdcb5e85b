#ifndef _SMS_PACKET_STRUCT_H_
#define _SMS_PACKET_STRUCT_H_
/* default v5_2 */

/*
 * bind send type = 1
 * bind ack type = 2
 * data send type = 3
 * data ack type = 4
 * report send type = 5 
 * report ack type = 6
 * link send type = 7
 * link ack type = 8
 */

typedef struct _TYPE_HEADER { /* common */
	char msgType[ 2];
	char msg<PERSON>eng[ 4];
} TypeHeader;

/****************** bind **********************/
typedef struct _TYPE_MSG_BIND_SND_v3 { /* v3 , v4 */
	TypeHeader header;
	char szCID[10];
	char szPWD[10];
	char classify  ; /* classify sender or report : S:sender R:report */
} TypeMsgBindSnd_v3;


typedef struct _TYPE_MSG_BIND_SND { /* v5 ,v5_2, v6 , v8 */
	TypeHeader header;
	char szCID[10];
	char szP<PERSON>[10];
	char szOS[10];
	char szVER[10];
	char classify  ; /* classify sender or report : S:sender R:report */
} TypeMsgBindSnd;


typedef struct _TYPE_MSG_BIND_ACK { /* common */
	TypeHeader header;
	char szResult[2];
} TypeMsgBindAck;

typedef struct _TYPE_MSG_GET_CALLBACK {
	TypeHeader header;
	int pid;
} TypeMsgGetCallback;

typedef struct _TYPE_MSG_GET_DIALCODE {
	TypeHeader header;
	char dial_code_type[4+1];
} TypeMsgGetDialCode;

/****************** send / link **********************/
typedef struct _TYPE_MSG_DATA_SND { /* v3, v5 , v5_2 */
	TypeHeader header;
	char szSerial[16];
	char szDstadr[16];
	char szCalbck[16];
        char szMsgType[4];
	char szSndmsg[86];
} TypeMsgDataSnd;

typedef struct _TYPE_MSG_DATA_SND_v4 { /* v4 , v8 */
        TypeHeader header;
        char szSerial[16];
        char szDstadr[16];
        char szCalbck[16];
        char szMsgType[4];
        char szSndmsg[86];
        char szReserv[86];
} TypeMsgDataSnd_v4;

typedef struct _TYPE_MSG_DATA_SND_v6 { /* v6 */
        TypeHeader header;
        char szSerial[16];
        char szDstadr[16];
        char szCalbck[16];
        char szMsgType[4];
        char szSndmsg[86];
        char szReserve[20+2];
} TypeMsgDataSnd_v6;

typedef struct _TYPE_MSG_DATA_SND_ENC { /* v9 */
        TypeHeader header;
        char szSerial[16];
        char szDstadr[16];
        char szCalbck[16];
        char szMsgType[4];
        char szSndmsg[96];
        char szReserve[96];
} TypeMsgDataSnd_enc;

/* link msg and send,rpt msg ack */
/* link send type = 7
 * link ack type = 8
 */
typedef struct _TYPE_MSG_DATA_ACK { /* common */
	TypeHeader header;
	char szSerial[16];
	char szResult[2];
} TypeMsgDataAck;

/****************** send / link **********************/

typedef struct _TYPE_MSG_DATA_RPT { /* v5_2, v8 */
	TypeHeader header;
	char szSerial[16];
	char szTelInfo[8];
	char szRptDate[16];
	char szResult[2];
} TypeMsgDataRpt;

typedef struct _TYPE_MSG_DATA_RPT_v3 { /* v3 v4 v5 v6 */
	TypeHeader header;
	char szSerial[16];
	char szTelInfo[8];
	char szResult[2];
} TypeMsgDataRpt_v3;

#endif

