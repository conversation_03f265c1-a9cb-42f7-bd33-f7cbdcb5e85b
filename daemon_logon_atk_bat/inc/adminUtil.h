#ifndef _ADMIN_UTIL_H_
#define _ADMIN_UTIL_H_

#include <dirent.h>

#include "stdafx.h"
#include "kssocket.h"
#include "errno.h"
#include "adminInfo.h"
#include "processInfo.h"
#include "smsPacketStruct.h"

class CAdminUtil {
    public:
        int createDomainID(char* id,char classify, char* path);
        int createDomainID(char* id,char classify, char* path, int domainDupMaxCount);
        int deleteDomainID(char* id,char classify, char* path);
        int checkPacket(CLogonDbInfo& logonDbInfo);
        int checkPacket(CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo);
        int checkPacket(CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,int sum);
        int findFileInit(char* path);
        char* findFileNext();
        char* findFileNext(char* pattern);
        int findFileClose();

        char* getErrMsg();
    private:
       CKSSocket m_svrSock;
       CKSSocket m_newSock;
       int m_hNewSocket;
       char buff[SOCKET_BUFF];
       char szErrMsg[512];
       CAdminInfo m_adminInfo;
       DIR *dp;

};



#endif
/* sample 

    CAdminUtil util;
    char* fileName = NULL;
    util.findFileInit("/home/<USER>/domain");
    while( (fileName = util.findFileNext() ) != NULL )
    {
        printf("[%s]\n",fileName);
    }
    util.findFileClose();


*/



