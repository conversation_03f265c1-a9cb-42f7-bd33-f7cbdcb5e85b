#ifndef _SMS_PACKET_STRUCT_KNBANK_H_
#define _SMS_PACKET_STRUCT_KNBANK_H_
#include "smsPacketStruct.h"

/****************** bind **********************/

/****************** send / link **********************/
typedef struct _TYPE_MSG_KNDATA_SND { /* kn bank */
	TypeHeader header;
	char szSerial[16];
	char sz<PERSON>tadr[16];
	char sz<PERSON><PERSON>bck[12];
        char szMsgType;
	char szSndmsg[80];
        char szBank<PERSON><PERSON>[67];
} TypeMsgKNDataSnd;

/* link msg and send,rpt msg ack */
/* link send type = 7
 * link ack type = 8
 */
typedef struct _TYPE_MSG_KNDATA_ACK { /* common */
	TypeHeader header;
	char sz<PERSON>erial[16];
	char szR<PERSON>ult[2];
        char sz<PERSON><PERSON><PERSON><PERSON><PERSON>[67];
} TypeMsgKNDataAck;

/****************** send / link **********************/

typedef struct _TYPE_MSG_KNDATA_RPT { /* kn bank */
	TypeHeader header;
	char szSerial[16];
	char szTelInfo[8];
        char szRptDate[16];
	char szResult[2];
        char szBankArea[67];
} TypeMsgKNDataRpt;


#endif

