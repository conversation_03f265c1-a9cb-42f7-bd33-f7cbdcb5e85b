#ifndef _REPORT_PROCESS_H_
#define _REPORT_RPOCESS_H_

#include "stdafx.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "logonUtil.h"
#include "adminUtil.h"
#include "reportDbInfo.h"
#include "processInfo.h"
#include "monitor.h"
#include "ksconfig.h"


class CConfigReport {
    public:
        char logonDBName[64];
        char monitorName[64];
        char domainPath[64];
        int socketLinkTime;
        int domainDupMaxCount;
};

CConfigReport gConf;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];



char reportDbDomainName[64];
void logPrintR(int type, const char *format, ...);
int classifyR(CKSSocket& hRemoteSock,char* buff);
int sendLink(CKSSocket& hRemoteSock,CLogonUtil& util);
int getReprot(CMonitor& monitor,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        CLogonUtil& util,
        char* szAppName,
        int nRptNoDataSleep);

int retryReport(CKSSocket& db,
        CReportDbInfo& rptData);

int configParse(char* file);

bool bRActive = true;
time_t RThisT,RLastTLink;
time_t monLastT;
int sum=0;
char szReportID[16];
char _DATALOG[64];
char _MONILOG[64];



// char* reportDbDomainName =  "/user/neosms/domain/DB_report_1";



void reportProcess(int sockfd,CLogonDbInfo& logonDbInfo);

void viewPackReport(char *a,int n);

#endif

