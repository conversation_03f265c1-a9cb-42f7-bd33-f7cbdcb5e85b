#ifndef _KSKYB_DB_UTIL_
#define _KSKYB_DB_UTIL_
#include "stdafx.h"
//#include <stdio.h>
#include <time.h>
#include <sys/time.h>
//#include <string>
#include "kssocket.h"
#include "senderDbInfo.h"


class CDBUtil {
    public:
        CDBUtil();
        ~CDBUtil();
        void* sendQuery(
                CKSSocket& db,
                void* data,
                void* ack,
                int dbRequestTimeOut ,
                char* senderDbDomainName);
        void* sendQuery(
        CKSSocket& db,
        void* data,
        void* ack,
        int dbRequestTimeOut, 
        char* senderDbDomainName,
        int& nResult);
        const char* getErrorMsg();
    private:
        std::string strErrorMsg;
};

#endif

