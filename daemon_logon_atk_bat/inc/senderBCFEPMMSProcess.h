#ifndef _SENDER_BCFEP_MMS_PROCESS_H_
#define _SENDER_BCFEP_MMS_PROCESS_H_

#include <string>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "fifoInfo.h"
#include "logonUtil.h"
#include "kssocket.h"
#include "adminInfo.h"
#include "adminUtil.h"
#include "senderDbInfo.h"
#include "processInfo.h"
#include "monitor.h"
#include "ksconfig.h"
#include "senderInfo.h"
#include "mmsPacketUtil.h"
#include "mmsPacketBase.h"
#include "mmsPacketSend.h"
#include "dbUtil.h"
#include "mmsFileProcess.h"

using namespace std;
#define TELCO_SKT "QUEUE_SKT"
#define TELCO_KTF "QUEUE_KTF"
#define TELCO_SSN "QUEUE_SKT"
#define TELCO_LGT TELCO_SSN

/*
#define TELCO_SKT "QUEUE_SSN"
#define TELCO_KTF "QUEUE_SSN"
#define TELCO_SSN "QUEUE_SSN"
#define TELCO_LGT TELCO_SSN
*/

class CConfigSender {
    public:
        char logonDBName[64];
        char monitorName[64];
        char domainPath[64];
        int socketLinkTimeOut;
        int dbRequestTimeOut;
        char ContentPath[64];
        char serverIP[16];
        int serverPORT;
        int process_sleep_time;
        char bindir[64];
        char logPath[64];
        char domainPath[64];
        char clientIP[256];
		char cfgfile[256];
        char szSenderName[64];
        char szSenderDBName[64];
        char szLogFilePath[128];
        char serverIP[16];
		char szReserve[128];
        int serverPORT;
};

CConfigSender gConf;
CSenderInfo gSenderInfo;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


void senderProcess(int sockfd,CLogonDbInfo& logonDbInfo);
void logPrintS(int type, const char *format, ...);
int classifyS(CMonitor& monitor,
        CProcessInfo& processInfo,
        CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);
int recvLink(CKSSocket& hRemoteSock,char* buff);
int recvSMS(CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);

int recvMMS(CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);
char*  get010Telco(char* key);


int send2DB(
        CKSSocket& db,
        CSenderDbInfo& senderDbInfo,
        CSenderDbInfoAck& ack);
int getMMSID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid);
int getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid);

int setMMSTBL2DB(CKSSocket& db,int nMMSId,int ctnid,
        int priority,
        CBcastData* pBcastData = NULL );
int setMMSMSG2DB(CKSSocket& db,int nMMSId,int ctnid,CMMSFileProcess& mmsFileProcess,
        int priority,
        CBcastData* pBcastData = NULL );



int setMMSCTNTBL2DB(CKSSocket& db,CMMSFileProcess& mmsFileProcess);

int sendPong(CKSSocket& hRemoteSock);
int sendAck(
        CKSSocket& hRemoteSock,
        CMMSPacketSend& mmsPacketSend,
        int nCode,
        int ctnid,
        string strDesc);

void writeLogMMSData(CMMSPacketSend& mmsPacketSend,int mmsid, int ctnid);

int configParse(char* file);

void viewPackSender(char *a,int n);
char* getTelcoQName(char* dstaddr,char* telco = NULL );



bool bSActive = true;
time_t SThisT,SLastTLink;
time_t monLastT;
int sum=0;
char szSenderID[64];
char _DATALOG[64];
char _MONILOG[64];

// char* senderDbDomainName =  "/user/neosms/domain/DB_sender_1";
char senderDbDomainName[64];

#endif

