#ifndef _LOGON_SESSION_H_
#define _LOGON_SESSION_H_

#include <iostream>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "kssocket.h"
#include "logonDbInfo.h"
#include "ksconfig.h"

using namespace std;


class CConfigLogonSession {
    public:
        char serverIP[16];
        int serverPORT;
        int process_sleep_time;
        char logonDBName[64];
        char bindir[64];
        char cfgdir[64];
        char logPath[64];
        char domainPath[64];
        char l4IP[64];
};

int activeProcess = TRUE;
struct _message_info    message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


CConfigLogonSession gConf;

int getAccept(int hOpenSocket,char* szIP,int nPort);
int requestLogon(int sockfd,CLogonDbInfo &logonDbInfo);
int checkServerInfo(CLogonDbInfo &logonDbInfo,char* szIP,int nPort);
int checkLoginDup(CLogonDbInfo &logonDbInfo);
int configParse(char* file);

#endif


