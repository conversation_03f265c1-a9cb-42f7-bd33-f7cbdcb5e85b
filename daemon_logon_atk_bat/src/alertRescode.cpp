#include "alertCommon.h"
#include "string.h"
#include <iostream>
#include <sqlca.h>

using namespace std;

/* Alert2Admin */
//#define CALL_WEB	"211.43.202.32"
#define CALL_WEB	"210.116.121.230"
#define PORT_WEB	80
#define WEB_PAGE	"/alertcall/alertcall.php"
#define	SVC_TYPE	 1
#define	CALL_TYPE	720
//#define SQLCODE "sqlca.sqlcode"

EXEC SQL BEGIN DECLARE SECTION;
char *DB_SID="NEOMMS";
char *DB_ID="NEOMMS";
char *DB_PASS="NEOMMS";
EXEC SQL END DECLARE SECTION;

char _DATALOG[64];
char _LOGBUF[2048];



int strReplace(char* sourceMsg , char* a , char* b)
{
    int cnt;
    char* pstrtmp;
    char msgtmp[512];
    pstrtmp = (char*)strstr(sourceMsg,a);
    while(pstrtmp)
    {
        memset(msgtmp,0x00,sizeof(msgtmp));
        memcpy(msgtmp,sourceMsg,(cnt = (int)(pstrtmp - sourceMsg)));
        memcpy(msgtmp+cnt,b,strlen(b));
        memcpy(msgtmp+cnt+strlen(b),pstrtmp+strlen(a),strlen(pstrtmp+strlen(a)));
        strcpy(sourceMsg,msgtmp);
        pstrtmp = (char*)strstr(pstrtmp+strlen(b),a);
    }
    return 1;
}


int Alert2Admin(char* pForm,...)
{
    int sockfd, len;
    struct sockaddr_in serv_addr;
    struct timeval tv;

    char strParam[256];
    char transBuf[350];

    va_list pArg;
    va_start(pArg, pForm);
    vsprintf(strParam, pForm, pArg);
    va_end(pArg);

    strReplace(strParam,(char*)" ",(char*)"%20");
    memset(transBuf,0x00,sizeof(transBuf));
    sprintf(transBuf,"get %s?%s\n",WEB_PAGE,strParam);
    
//    printf("\nAlert2Admin:[%s]\n",transBuf);

		sprintf(_LOGBUF,"[DEBUG] Alert2Admin transBuf[%s]", transBuf);
		_logPrint(_DATALOG, _LOGBUF);
		
		
    memset((char*)&serv_addr,0x00,sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(CALL_WEB);
    serv_addr.sin_port = htons(PORT_WEB);

    if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
        return -1;
    }

    tv.tv_sec = 5;
    tv.tv_usec = 0;

    if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
    }

    if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
    }

    if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
        return -1;
    }
    
    len = write(sockfd,transBuf,strlen(transBuf));
    if( len < 0 )
        return -1;
    memset(transBuf,0x00,sizeof(transBuf));
    len = read(sockfd,transBuf,sizeof(transBuf));
    close(sockfd);

		//sprintf(_LOGBUF,"[DEBUG] recv transBuf[%s]", transBuf);
		//_logPrint(_DATALOG, _LOGBUF);
		    
    if (strncmp(&transBuf[len-2],"OK",2) != 0 )
        return -1;
		    
    return 0;
}


char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

void get_timestring2(char *s)
{
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(s, "%04d%02d%02d,%02d:%02d:%02d.%09d",
		tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday,
		tp.tm_hour, tp.tm_min, tp.tm_sec,
		(int)tmv.tv_nsec
		);
    s[strlen(s)] = ' ';
}

int _logPrint(char* szPath, char* szLog)
{
    static int fd=0;
    int writeLen;

    static char file[256]="";
    char date[32];
    char buff[2048];
    static char current_date[16]="";

    memset(date,0x00,sizeof(date));
    memset(buff,0x00,sizeof(buff));
    get_timestring2(date);


    if( memcmp(date,current_date,8) == 0 ) {
        /* 이미 있는 파일 */
        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }



    } else {
        /* 새로 만들어야 하는 파일 */
        if( fd ) close(fd);
        strncpy(current_date, date, 8);
        current_date[8] = '\0';

/*        strcpy(file, LOGFILEDIR);
 */
        strcpy(file, szPath);
        strcat(file, ".");
        strcat(file, current_date);

        fd = open( file, CREATE_FLAGS, CREATE_PERMS );
        if( fd < 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"log File Open fail : [%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }

        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }


    }

    return 0;
}



void combine_dst(char *dst, char *addr1, char *addr2, char *addr3 )
{

	if( strlen(addr1) > 0 )
		strcpy(dst, addr1 );

	if( strlen(addr2) > 0 )
	{
		strcat(dst, "|");
		strcat(dst, addr2 );
	}
	if( strlen(addr3) > 0 )
	{
		strcat(dst, "|");
		strcat(dst, addr3 );
	}

	return;
}

int procHourAll() 
{

	char dst_addr[64];
	char job_type[64];
	
  EXEC SQL BEGIN DECLARE SECTION;
  char cid[40+1];
  char cpname[50+1];
	char res_code[10+1];
	char res_text[50+1];
	char dst_addr1[16+1];
	int	 res_cnt;
  EXEC SQL END DECLARE SECTION;
	
	sprintf(_LOGBUF,"procHourAll start!!! " );
	_logPrint(_DATALOG, _LOGBUF);
	
	// 1시간 쿼리
	// 전체 CID
	EXEC SQL DECLARE cur_res_code_h_all CURSOR FOR 
  SELECT CID_1, PTN_INFO.C_CPNAME_VCH, RES_CODE_1, RES_TEXT_1, DST_ADDR_1, CNT_1  
    FROM 
    (
      SELECT LOGON.PID PID_1, LOGON.CID CID_1, AA.BB RES_CODE_1, AA.CC RES_TEXT_1, AA.DD DST_ADDR_1, COUNT(AA.BB) CNT_1 
      FROM TBL_MMS_SEND SND, TBL_LOGON LOGON,  
      (  
          SELECT RPT.MMS_ID MMS_ID, RES.RES_TEXT CC, RPT.RES_CODE BB, RES.DST_ADDR DD  
          FROM TBL_ALERT_RES_CODE  RES, TBL_MMS_RPT RPT 
          WHERE RES.USE_YN='Y' 
          AND RES.PERIOD='H' 
          AND RPT.RES_CODE=RES.RES_CODE  
          AND RPT.RES_DATE > SYSDATE - 1/24
        ) AA
      WHERE AA.MMS_ID=SND.MMS_ID
      AND SND.CID=LOGON.CID
      GROUP BY LOGON.PID, LOGON.CID , AA.BB , AA.CC, AA.DD
    ) , TBL_PTN_INFO PTN_INFO 
    WHERE PID_1 = PTN_INFO.PTN_ID AND CID_1 NOT IN (SELECT CID FROM TBL_ALERT_RES_CODE_CID) 
    AND CNT_1 >= (SELECT THRESHOLD FROM TBL_ALERT_RES_CODE WHERE RES_CODE=RES_CODE_1 AND USE_YN='Y' AND PERIOD='H' );
    
	sprintf(job_type, "시간당 발생건수");
	
			
	EXEC SQL OPEN cur_res_code_h_all;
			
	EXEC SQL WHENEVER NOT FOUND DO break;
	
	
	while(1)
	{

		memset(cid, 0x00, sizeof(cid));
		memset(cpname, 0x00, sizeof(cpname));
		memset(res_code, 0x00, sizeof(res_code));
		memset(res_text, 0x00, sizeof(res_text));				
		memset(dst_addr1, 0x00, sizeof(dst_addr1));
	
		EXEC SQL FETCH cur_res_code_h_all INTO :cid, :cpname, :res_code, :res_text, :dst_addr1, :res_cnt;

			
		if( sqlca.sqlcode != 0 && sqlca.sqlcode != -1405 )
		{
			EXEC SQL CLOSE cur_res_code_h_all;
			//EXEC SQL COMMIT WORK RELEASE;
			
			sprintf(_LOGBUF,"[ERR] FETCH Fail(err : %d ) cid[%s] res_code[%s]", sqlca.sqlcode, cid, res_code );
			_logPrint(_DATALOG, _LOGBUF);
			return -1;
		}

		trim(cid, strlen(cid));
		trim(cpname, strlen(cpname));
		trim(res_code, strlen(res_code));
		trim(res_text, strlen(res_text));
		trim(dst_addr1, strlen(dst_addr1));


		if ( Alert2Admin("msgbody=%s(%s) CID:%s %s(%d)건&type=%d&callType=%d&phoneNum=%s&cpName=%s", res_text, res_code, cid, job_type, res_cnt, SVC_TYPE, CALL_TYPE, dst_addr1, cpname ) < 0 )
		{
			sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] res_code[%s] job_type[%s] dst_addr[%s]", cid, res_code, job_type, dst_addr1);
			_logPrint(_DATALOG, _LOGBUF);

		}


	} // while
	
	EXEC SQL CLOSE cur_res_code_h_all;
	//EXEC SQL COMMIT WORK RELEASE;
	

    return 0;
}

int procHour() 
{

	char dst_addr[64];
	char job_type[64];
	
  EXEC SQL BEGIN DECLARE SECTION;
  char cid[40+1];
  char cpname[50+1];
	char res_code[10+1];
	char res_text[50+1];
	char dst_addr1[16+1];
	char dst_addr2[16+1];
	char dst_addr3[16+1];		
	int	 res_cnt;
  EXEC SQL END DECLARE SECTION;
	
	sprintf(_LOGBUF,"procHour start!!! " );
	_logPrint(_DATALOG, _LOGBUF);
	
	// 1시간 쿼리
	// 등록업체만 처리한다
	EXEC SQL DECLARE cur_res_code_h CURSOR FOR 
	SELECT CID_1, CPNAME_1, RES_CODE_1, RES_TEXT_1, DSTADDR_1, DSTADDR_2, DSTADDR_3, CNT_1  
	FROM 
	(
	  SELECT SND.CID CID_1, AA.BB RES_CODE_1, AA.DD CPNAME_1, AA.EE RES_TEXT_1, AA.GG DSTADDR_1, AA.HH DSTADDR_2, AA.II DSTADDR_3, COUNT(AA.BB) CNT_1 
	  FROM TBL_MMS_SEND SND,
	  (  
		  SELECT RPT.MMS_ID MMS_ID, ZZ.BB BB, ZZ.CC CC, ZZ.DD DD, ZZ.EE EE, ZZ.GG GG, ZZ.HH HH, ZZ.II II
		  FROM
		  (
				SELECT RES_CID.RES_CODE BB, RES_CID.CID CC, RES_CID.CPNAME DD, RES.RES_TEXT EE, RES_CID.DST_ADDR1 GG, RES_CID.DST_ADDR2 HH, RES_CID.DST_ADDR3 II  
		  	FROM TBL_ALERT_RES_CODE_CID RES_CID, TBL_ALERT_RES_CODE RES WHERE RES_CID.RES_CODE=RES.RES_CODE AND RES_CID.USE_YN='Y' AND RES.USE_YN='Y' AND RES.PERIOD='H' 
		  ) ZZ, TBL_MMS_RPT RPT
		  WHERE RPT.RES_CODE=ZZ.BB  
		  AND RPT.RES_DATE > SYSDATE - 1/24
		) AA
	  WHERE AA.MMS_ID=SND.MMS_ID
	  AND SND.CID=AA.CC
	  GROUP BY SND.CID, AA.BB, AA.DD, AA.EE, AA.GG,AA.HH, AA.II
	)
	WHERE CNT_1 >= (SELECT THRESHOLD FROM TBL_ALERT_RES_CODE WHERE RES_CODE=RES_CODE_1 AND USE_YN='Y' AND PERIOD='H' );
	
	sprintf(job_type, "시간당 발생건수");
	
			
	EXEC SQL OPEN cur_res_code_h;
			
	EXEC SQL WHENEVER NOT FOUND DO break;
	
	
	while(1)
	{

		memset(cid, 0x00, sizeof(cid));
		memset(cpname, 0x00, sizeof(cpname));
		memset(res_code, 0x00, sizeof(res_code));
		memset(res_text, 0x00, sizeof(res_text));				
		memset(dst_addr1, 0x00, sizeof(dst_addr1));
		memset(dst_addr2, 0x00, sizeof(dst_addr2));
		memset(dst_addr3, 0x00, sizeof(dst_addr3));
	
		EXEC SQL FETCH cur_res_code_h INTO :cid, :cpname, :res_code, :res_text, :dst_addr1, :dst_addr2, :dst_addr3, :res_cnt;

			
		if( sqlca.sqlcode != 0 && sqlca.sqlcode != -1405 )
		{
			EXEC SQL CLOSE cur_res_code_h;
			//EXEC SQL COMMIT WORK RELEASE;
			
			sprintf(_LOGBUF,"[ERR] FETCH Fail(err : %d ) cid[%s] res_code[%s]", sqlca.sqlcode, cid, res_code );
			_logPrint(_DATALOG, _LOGBUF);
			return -1;
		}

		trim(cid, strlen(cid));
		trim(cpname, strlen(cpname));
		trim(res_code, strlen(res_code));
		trim(res_text, strlen(res_text));
		trim(dst_addr1, strlen(dst_addr1));
		trim(dst_addr2, strlen(dst_addr2));
		trim(dst_addr3, strlen(dst_addr3));


		combine_dst( dst_addr, dst_addr1, dst_addr2, dst_addr3 );


		if ( Alert2Admin("msgbody=%s(%s) CID:%s %s(%d)건&type=%d&callType=%d&phoneNum=%s&cpName=%s", res_text, res_code, cid, job_type, res_cnt, SVC_TYPE, CALL_TYPE, dst_addr, cpname ) < 0 )
		{
			sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] res_code[%s] job_type[%s] dst_addr[%s]", cid, res_code, job_type, dst_addr);
			_logPrint(_DATALOG, _LOGBUF);

		}


	} // while
	
	EXEC SQL CLOSE cur_res_code_h;
	//EXEC SQL COMMIT WORK RELEASE;
	

    return 0;
}

int procDayAll() 
{

	char dst_addr[64];
	char job_type[64];
	
  EXEC SQL BEGIN DECLARE SECTION;
  char cid[40+1];
  char cpname[50+1];
	char res_code[10+1];
	char res_text[50+1];
	char dst_addr1[16+1];	
	int	 res_cnt;
  EXEC SQL END DECLARE SECTION;
	
	sprintf(_LOGBUF,"procDayAll start!!! " );
	_logPrint(_DATALOG, _LOGBUF);
	
	EXEC SQL DECLARE cur_res_code_d_all CURSOR FOR 
	SELECT CID_1, CPNAME_1, RES_CODE_1, RES_TEXT_1, DST_ADDR_1, SUM(CNT_1) 
	FROM 
	(
		SELECT STATIS.CID CID_1, STATIS.C_CPNAME_VCH CPNAME_1, STATIS.RES_CODE RES_CODE_1,  RES.DST_ADDR DST_ADDR_1,  RES.RES_TEXT RES_TEXT_1, STATIS.CNT CNT_1 
		FROM TBL_STATISTICS_MMS STATIS, TBL_ALERT_RES_CODE RES
		WHERE STATIS.RES_CODE=RES.RES_CODE 
		AND RES.USE_YN='Y' AND RES.PERIOD='D'  
		AND STATIS.C_DATE = TO_CHAR(SYSDATE - 1, 'YYYYMMDD')
	) 
	WHERE CID_1 NOT IN (SELECT CID FROM TBL_ALERT_RES_CODE_CID) 
	AND CNT_1 >= (SELECT THRESHOLD FROM TBL_ALERT_RES_CODE WHERE RES_CODE=RES_CODE_1 AND USE_YN='Y' AND PERIOD='D')
	GROUP BY CID_1, CPNAME_1, RES_CODE_1, RES_TEXT_1, DST_ADDR_1;
  
  sprintf(job_type, "일일 발생건수");		

	
			
	EXEC SQL OPEN cur_res_code_d_all;
	EXEC SQL WHENEVER NOT FOUND DO break;
	
	while(1)
	{

		memset(cid, 0x00, sizeof(cid));
		memset(cpname, 0x00, sizeof(cpname));
		memset(res_code, 0x00, sizeof(res_code));
		memset(res_text, 0x00, sizeof(res_text));				
		memset(dst_addr1, 0x00, sizeof(dst_addr1));						
	
		EXEC SQL FETCH cur_res_code_d_all INTO :cid, :cpname, :res_code, :res_text, :dst_addr1,:res_cnt;

		if( sqlca.sqlcode != 0 && sqlca.sqlcode != -1405 )
		{
			EXEC SQL CLOSE cur_res_code_d_all;
			//EXEC SQL COMMIT WORK RELEASE;
			
			sprintf(_LOGBUF,"[ERR] FETCH Fail(err : %d ) cid[%s] res_code[%s]", sqlca.sqlcode, cid, res_code );
			_logPrint(_DATALOG, _LOGBUF);
			return -1;
		}

		trim(cid, strlen(cid));
		trim(cpname, strlen(cpname));
		trim(res_code, strlen(res_code));
		trim(res_text, strlen(res_text));
		trim(dst_addr1, strlen(dst_addr1));
		

		//combine_dst( dst_addr, dst_addr1, dst_addr2, dst_addr3 );
				
//		sprintf(_LOGBUF,"[DEBUG] %s cid[%s] res_code[%s][%s] cnt[%d] job_type[%s]", job_type, cid, res_code, res_text, res_cnt, job_type);
//		_logPrint(_DATALOG, _LOGBUF);


if ( Alert2Admin("msgbody=%s(%s) CID:%s %s(%d)건&type=%d&callType=%d&phoneNum=%s&cpName=%s", res_text, res_code, cid, job_type, res_cnt, SVC_TYPE, CALL_TYPE, dst_addr1, cpname ) < 0 )
		{
			sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] res_code[%s] job_type[%s] dst_addr[%s]", cid, res_code, job_type, dst_addr1);
			_logPrint(_DATALOG, _LOGBUF);

		}


	} // while
	
	EXEC SQL CLOSE cur_res_code_d_all;
	//EXEC SQL COMMIT WORK RELEASE;
	

    return 0;
}

int procDay() 
{

	char dst_addr[64];
	char job_type[64];
	
  EXEC SQL BEGIN DECLARE SECTION;
  char cid[40+1];
  char cpname[50+1];
	char res_code[10+1];
	char res_text[50+1];
	char dst_addr1[16+1];
	char dst_addr2[16+1];
	char dst_addr3[16+1];		
	int	 res_cnt;
  EXEC SQL END DECLARE SECTION;
	
	sprintf(_LOGBUF,"procDay start!!! " );
	_logPrint(_DATALOG, _LOGBUF);
	
	EXEC SQL DECLARE cur_res_code_d CURSOR FOR 
	SELECT CID_1, CPNAME_1, RES_CODE_1, RES_TEXT_1, DSTADDR_1, DSTADDR_2, DSTADDR_3, SUM(CNT_1) 
	FROM 
	(
	  SELECT STATIS.CID CID_1, STATIS.RES_CODE RES_CODE_1, STATIS.C_CPNAME_VCH CPNAME_1, AA.EE RES_TEXT_1, AA.GG DSTADDR_1, AA.HH DSTADDR_2, AA.II DSTADDR_3, STATIS.CNT CNT_1 
	  FROM TBL_STATISTICS_MMS STATIS, 
		(
			SELECT RES_CID.RES_CODE BB, RES_CID.CID CC, RES.RES_TEXT EE, RES_CID.DST_ADDR1 GG, RES_CID.DST_ADDR2 HH, RES_CID.DST_ADDR3 II 
			FROM TBL_ALERT_RES_CODE_CID RES_CID, TBL_ALERT_RES_CODE RES WHERE RES_CID.RES_CODE=RES.RES_CODE AND RES_CID.USE_YN='Y' AND RES.USE_YN='Y' AND RES.PERIOD='D'  
		) AA
		WHERE STATIS.RES_CODE=AA.BB AND STATIS.CID=AA.CC 
		AND STATIS.C_DATE = TO_CHAR(SYSDATE - 1, 'YYYYMMDD')
	) 
	WHERE CNT_1 >= (SELECT THRESHOLD FROM TBL_ALERT_RES_CODE WHERE RES_CODE=RES_CODE_1 AND USE_YN='Y' AND PERIOD='D')
	GROUP BY CID_1, CPNAME_1, RES_CODE_1, RES_TEXT_1, DSTADDR_1, DSTADDR_2, DSTADDR_3;
	
	sprintf(job_type, "일일 발생건수");		

	
			
	EXEC SQL OPEN cur_res_code_d;
	EXEC SQL WHENEVER NOT FOUND DO break;
	
	while(1)
	{

		memset(cid, 0x00, sizeof(cid));
		memset(cpname, 0x00, sizeof(cpname));
		memset(res_code, 0x00, sizeof(res_code));
		memset(res_text, 0x00, sizeof(res_text));				
		memset(dst_addr1, 0x00, sizeof(dst_addr1));
		memset(dst_addr2, 0x00, sizeof(dst_addr2));
		memset(dst_addr3, 0x00, sizeof(dst_addr3));
	
		EXEC SQL FETCH cur_res_code_d INTO :cid, :cpname, :res_code, :res_text, :dst_addr1, :dst_addr2, :dst_addr3, :res_cnt;

		if( sqlca.sqlcode != 0 && sqlca.sqlcode != -1405 )
		{
			EXEC SQL CLOSE cur_res_code_d;
			//EXEC SQL COMMIT WORK RELEASE;
			
			sprintf(_LOGBUF,"[ERR] FETCH Fail(err : %d ) cid[%s] res_code[%s]", sqlca.sqlcode, cid, res_code );
			_logPrint(_DATALOG, _LOGBUF);
			return -1;
		}

		trim(cid, strlen(cid));
		trim(cpname, strlen(cpname));
		trim(res_code, strlen(res_code));
		trim(res_text, strlen(res_text));
		trim(dst_addr1, strlen(dst_addr1));
		trim(dst_addr2, strlen(dst_addr2));
		trim(dst_addr3, strlen(dst_addr3));


		combine_dst( dst_addr, dst_addr1, dst_addr2, dst_addr3 );
				
//		sprintf(_LOGBUF,"[DEBUG] %s cid[%s] res_code[%s][%s] cnt[%d] job_type[%s]", job_type, cid, res_code, res_text, res_cnt, job_type);
//		_logPrint(_DATALOG, _LOGBUF);


if ( Alert2Admin("msgbody=%s(%s) CID:%s %s(%d)건&type=%d&callType=%d&phoneNum=%s&cpName=%s", res_text, res_code, cid, job_type, res_cnt, SVC_TYPE, CALL_TYPE, dst_addr, cpname ) < 0 )
		{
			sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] res_code[%s] job_type[%s] dst_addr[%s]", cid, res_code, job_type, dst_addr);
			_logPrint(_DATALOG, _LOGBUF);

		}


	} // while
	
	EXEC SQL CLOSE cur_res_code_d;
	//EXEC SQL COMMIT WORK RELEASE;
	

    return 0;
}

int main(int argc, char* argv[])
{
  int ret;
  char arg;

	sprintf(_DATALOG, "/data/log_alert/ALERT_RES_CODE");

	if( argc != 2 )
	{
		sprintf( _LOGBUF, "Arg insert!! ex) alertRescode H (or D)");
		_logPrint(_DATALOG, _LOGBUF);
		return 1;
	}
	
	arg = *argv[1];
	
    
	sprintf(_LOGBUF,"alertRescode Process START!!! TYPE[%c]", arg );
	_logPrint(_DATALOG, _LOGBUF);

	EXEC SQL CONNECT :DB_ID IDENTIFIED BY :DB_PASS USING :DB_SID;
	if(sqlca.sqlcode != 0 )
	{
	  sprintf(_LOGBUF,"[ERR] db connect failed[%d]\n- SID[%s]\n- ID[%s]\n- PW[%s]", sqlca.sqlcode, DB_SID, DB_ID, DB_PASS);
		_logPrint(_DATALOG, _LOGBUF);
	    goto END;
	}

	// 시간과 일별로 호출한다.
	// 전체 CID별로 한번 등록된 CID별로 한번 실행한다.
	if( arg == 'H')
	{
		procHourAll();
		procHour();
	}
	if( arg == 'D')
	{
		procDayAll();
		procDay();		
	}
		

	EXEC SQL COMMIT WORK RELEASE;
	
  sprintf(_LOGBUF,"alertRescode Process END!!! " );
	_logPrint(_DATALOG, _LOGBUF);


	return 0;



END:
	sprintf(_LOGBUF,"alertRescode Process END!!! " );
	_logPrint(_DATALOG, _LOGBUF);

	EXEC SQL COMMIT WORK RELEASE;
  return 0;
}
