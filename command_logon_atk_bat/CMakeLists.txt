cmake_minimum_required(VERSION 3.10)
project(command_logon_atk_bat C)

set(CMAKE_C_STANDARD 99)

# Include directories
include_directories(inc)

# Executables for logon command project
add_executable(cmd_logon src/sms_command.c)
target_include_directories(cmd_logon PRIVATE inc)

add_executable(mnt_logon src/sms_monitor.c src/sms_ctrlsub.c)
target_include_directories(mnt_logon PRIVATE inc)

add_executable(log_logon src/sms_logger.c src/sms_ctrlsub.c)
target_include_directories(log_logon PRIVATE inc)

add_executable(dog_logon src/sms_watchdog.c src/sms_ctrlsub.c)
target_include_directories(dog_logon PRIVATE inc)

add_executable(crt_logon src/create_table.c)
target_include_directories(crt_logon PRIVATE inc)

# Set output directories
set_target_properties(cmd_logon PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "cmd"
)

set_target_properties(mnt_logon PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "mnt"
)

set_target_properties(log_logon PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "log"
)

set_target_properties(dog_logon PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "dog"
)

set_target_properties(crt_logon PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/cfg"
    OUTPUT_NAME "crt"
)

# Add custom target for Makefile build
add_custom_target(makefile_build_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add custom target for Makefile clean
add_custom_target(makefile_clean_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)
