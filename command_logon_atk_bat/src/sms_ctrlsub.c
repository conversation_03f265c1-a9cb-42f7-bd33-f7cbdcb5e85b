/*
 * Copyright (c) (주)메타랜드
 * Program Name : ml_ctrlsub.c
 * Comments     : 메타랜드 통합 쇼핑몰 관리를 위한 내부 통신 관련 Module들
 * ----------------------------------------------------------------------------
 * History
 *    [ 1] Initial Coding						97.12.30 InMok,Song
 *    [ 2] 지정Process에게 전송 루틴 추가			98. 1.15 InMok,Song
 */

#include    <stdio.h>
#include    <stdlib.h>
#include    <string.h>
#include    <unistd.h>
#include    <signal.h>
#include    <sys/types.h>
#include    <sys/ipc.h>
#include    <sys/shm.h>
#include    <sys/msg.h>
#include    <errno.h>
#include    <time.h>
#include    <stdarg.h>

#include	<sys/socket.h>
#include	<arpa/inet.h>
#include    <netinet/in.h>

#include    <code_info.h>
#include    <shm_info.h>
#include    <group_shm_info.h>
#include    <message_info.h>
#include    <ml_ctrlsub.h>

extern struct	_message_info	message_info;
extern struct _shm_info *              shm_info;

static int CHECK_Q_ID;

int         MONITOR_Q_ID,        MONITOR_SHM_INDEX = -1;
int         LOGGER_Q_ID,         LOGGER_SHM_INDEX  = -1;
int         MY_Q_ID,             MY_SHM_INDEX = -1;
int         SEND_GROUP_Q_ID[256], SEND_GROUP_SHM_INDEX[256];
int         SEND_GROUP_INDEX,    LAST_SEND_GROUP_INDEX = -1, MAX_SEND_GROUP = -1;

key_t quenum;	
char queueKey[100];
		
int         timeout_flag;

int         OS_PROCESS_NO;
char        MY_PROCESS_NO   [ 7];
char        MY_PROCESS_NAME [64];

static inline void get_process_name(char *, char *);
static int  get_process_id  (char *);
static char *itoa_reverse   (int);
static void safe_strcpy     (char *, char *);
static void timeout         (int);
/*
 * 초기화 Module
 */
int ml_sub_init(char* pm_process_no, char* pm_process_name, char* pm_shm_info, char* pm_group_shm_info)
{
    char	temp[256], temp_file[256];
    int		SHM_ID,    SHM_SIZE;
    int		be;
    FILE*	temp_fd;

#define MYIDX       shm_info->process_info[MY_SHM_INDEX]
#define BEIDX       shm_info->process_info[be]

    /* KERNEL에서 Process Name을 구한다. */
    OS_PROCESS_NO = getpid();
    strcpy(temp_file, "background_processid_");
    strcat(temp_file, itoa_reverse(OS_PROCESS_NO));
    unlink(temp_file);
    strcpy(temp, "ps -ef > ");
    strcat(temp, temp_file);
    system(temp);
    if ((temp_fd = fopen(temp_file, "r")) == NULL)
    {
        printf("in ml_ctrlsub : %s fopen error.\n", temp_file);
        exit(1);
    }
    be = 0;
    while (fgets(temp, sizeof(temp), temp_fd) != NULL)
    {
        if (OS_PROCESS_NO == get_process_id(temp))
        {
            get_process_name(temp, MY_PROCESS_NAME);
            be = 1;
            break;
        }
    }
    fclose(temp_fd);
    unlink(temp_file);


    if (!be)
    {
        printf("in ml_sub_init : KERNEL에서 Process ID %d를 구할수 없습니다.\n", OS_PROCESS_NO);
        return -1;
    }

    /* get shared memory information */
    SHM_SIZE = sizeof(struct _shm_info);
    if ((SHM_ID = shmget((key_t)SYS_SHM_KEY, SHM_SIZE, PERMS | IPC_CREAT)) < 0)
    {
        printf("%s shmget error.\n", MY_PROCESS_NAME);
        ml_sub_end();
        return -1;
    }

    if ((shm_info = (struct _shm_info *)shmat(SHM_ID, (char *)0, 0)) == (struct _shm_info *)-1)
    {
        printf("%s shmat error. SHM_ID[%d]\n", MY_PROCESS_NAME,SHM_ID);
        ml_sub_end();
        return -1;
    }

    if (pm_shm_info)  pm_shm_info = (char *)shm_info;
    /* get process information */
    for (be = 0; be < shm_info->sys_info.max_process; be ++)
    {
        MY_SHM_INDEX ++;
        if (strcmp(MY_PROCESS_NAME, shm_info->process_info[MY_SHM_INDEX].process_name) == 0)
            break;
    }
    
    if (be >= shm_info->sys_info.max_process)
    {
        printf("There is not exist process<%s> in <0x%x>\n", MY_PROCESS_NAME, SYS_SHM_KEY);
	printf("<0x%x> process list\n",SYS_SHM_KEY);
	/* get process information */
	for (be = 0; be < shm_info->sys_info.max_process; be ++)
	{
	    printf("[%s]\n",shm_info->process_info[be].process_name);
	}
	printf("<0x%x> process list end\n",SYS_SHM_KEY);
    
        ml_sub_end();
        return -1;
    }

    safe_strcpy (MY_PROCESS_NO,   shm_info->process_info[MY_SHM_INDEX].process_no);
    safe_strcpy (pm_process_no,   MY_PROCESS_NO);
    safe_strcpy (pm_process_name, MY_PROCESS_NAME);

    shm_info->process_info[MY_SHM_INDEX].process_status = ACTIVE_STATUS;

    /* get group shared memory information */
    if (MYIDX.shm_key > 0)
    {
        SHM_SIZE = sizeof(struct _group_shm_info);
        if ((SHM_ID = shmget((key_t)MYIDX.shm_key,SHM_SIZE, PERMS | IPC_CREAT)) < 0)
        {
            printf("%s group shmget error.\n", MY_PROCESS_NAME);
            ml_sub_end();
            return -1;
        }
        if ((group_shm_info = (struct _group_shm_info *)shmat(SHM_ID, (char *)0, 0))
            == (struct _group_shm_info *)-1)
        {
            printf("%s group shmat error.\n", MY_PROCESS_NAME);
            ml_sub_end();
            return -1;
        }
        if (pm_group_shm_info) pm_group_shm_info = (char *)group_shm_info;
    }

    /* Get My Message Queue */
    if (MYIDX.my_q_key > 0)
    {
    memset(queueKey,0x00,sizeof(queueKey));
    sprintf(queueKey,"0x%d",MYIDX.my_q_key);
	sscanf(queueKey, "%x", &quenum);
        
        if ((MY_Q_ID = msgget(quenum, PERMS | IPC_CREAT)) < 0)
        {
            printf("%s monitor msgget error.\n", MY_PROCESS_NAME);
            ml_sub_end();
            return -1;
        }               
    }
    
    if (MYIDX.process_type == MONITOR_TYPE)
    {
        MONITOR_SHM_INDEX = MY_SHM_INDEX;
        MONITOR_Q_ID      = MY_Q_ID;
        return 0;
    }

    /* Get Monitor Message Q */
    if (MYIDX.monitor_process_no > 0)
    {
        for (be = 0; be < shm_info->sys_info.max_process; be ++)
        {
            if (MYIDX.monitor_process_no == atoi(BEIDX.process_no)
             && BEIDX.process_type       == MONITOR_TYPE
             && BEIDX.my_q_key           >  0)
            {
                MONITOR_SHM_INDEX = be;


    memset(queueKey,0x00,sizeof(queueKey));
    sprintf(queueKey,"0x%d",BEIDX.my_q_key);
	sscanf(queueKey, "%x", &quenum);
        
                        
                if ((MONITOR_Q_ID = msgget(quenum, PERMS | IPC_CREAT)) < 0)
                {
                    printf("%s monitor msgget error.\n", MY_PROCESS_NAME);
                    ml_sub_end();
                    return -1;
                }
                break;
            }
        }
    }

    /* Get Logger Message Q */
    if (MYIDX.logger_process_no > 0)
    {
        for (be = 0; be < shm_info->sys_info.max_process; be ++)
        {
            if (MYIDX.logger_process_no == atoi(BEIDX.process_no)
             && BEIDX.process_type      == LOGGER_TYPE
             && BEIDX.my_q_key          >  0)
            {
                LOGGER_SHM_INDEX = be;


    memset(queueKey,0x00,sizeof(queueKey));
    sprintf(queueKey,"0x%d",BEIDX.my_q_key);
	sscanf(queueKey, "%x", &quenum);
        
        
                if ((LOGGER_Q_ID = msgget(quenum, PERMS | IPC_CREAT)) < 0)
                {
                    printf("%s monitor msgget error.\n", MY_PROCESS_NAME);
                    ml_sub_end();
                    return -1;
                }
                break;
            }
        }
    }
/*
    if( CHECK_Q_ID = msgget(CHECK_QUEUE_KEY, PERMS | IPC_CREAT) < 0 )
    {
        
        printf("msgget fail [%s]\n",MY_PROCESS_NAME);
        ml_sub_end();        
        return -1;   
    }
  */  
  
    CHECK_Q_ID = msgget(CHECK_QUEUE_KEY, PERMS | IPC_CREAT);
    if ( CHECK_Q_ID < 0 )
    {
            printf("msgget fail [%s]\n", MY_PROCESS_NAME);
            ml_sub_end();        
            return -1;       
    }

  
    /* Get Send Group Message Q */
    if (MYIDX.send_group_no > 0)
    {
        for (be = 0; be < shm_info->sys_info.max_process; be ++)
        {
            if (MYIDX.send_group_no == (atoi(BEIDX.process_no)/100)
             && BEIDX.my_q_key      > 0)
            {
                MAX_SEND_GROUP ++;
                if (MAX_SEND_GROUP > 99) return -100;
                SEND_GROUP_SHM_INDEX[MAX_SEND_GROUP] = be;
                
    memset(queueKey,0x00,sizeof(queueKey));
    sprintf(queueKey,"0x%d",BEIDX.my_q_key);
	sscanf(queueKey, "%x", &quenum);
                        
                if ((SEND_GROUP_Q_ID[MAX_SEND_GROUP] = msgget(quenum, PERMS | IPC_CREAT)) < 0)
                {
                    printf("%s send_group %s msgget error.\n", MY_PROCESS_NAME, BEIDX.process_no);
                    ml_sub_end();
                    return -1;
                }
            }
        }
    }

    return 0;
}

/* MONITOR Process에 전송 Module */
int ml_sub_send_moni(char* pm_send_buffer, int pm_send_length, int pm_wait_sec, int pm_status_code, int pm_os_error_no)
{
    struct      sigaction sa;

#define MD  message_info.msg.s_buffer

    message_info.mtype  = 1;
    MD.message_id       = MONITOR_MSG;
    MD.message_sub_id   = DEFAULT_SUB_MSG;
    strcpy(MD.process_no,   MY_PROCESS_NO);
    strcpy(MD.process_name, MY_PROCESS_NAME);
    MD.os_process_no    = getpid();
    MD.trans_time       = time(NULL);
    MD.select_flag[0]   = '0';
    MD.status_code      = pm_status_code;
    MD.os_error_no      = pm_os_error_no;
    MD.msg_length       = pm_send_length;
    memcpy(MD.message, pm_send_buffer, pm_send_length);

    /* Monitor 존재여부를 확인 */

    if (MONITOR_SHM_INDEX < 0) return -1;

	if(kill(shm_info->process_info[MONITOR_SHM_INDEX].os_process_no, 0) == 0)
	{
		if (pm_wait_sec < 0) pm_wait_sec = 0;
		sa.sa_handler = timeout;
		sigaction(SIGALRM, &sa, NULL);
		alarm(pm_wait_sec);
		if (msgsnd(MONITOR_Q_ID, (char *)&message_info, pm_send_length + MSG_HEADER_LEN, 0) != 0)
		{
			alarm(0);
			return -1;
		}
		alarm(0);
		return pm_send_length;
    }

    return -1;
}

/*
 * LOGGER Process에 전송 Module
 */
int ml_sub_send_log (char *pm_send_buffer,
		int pm_send_length,
		int pm_wait_sec,
		int pm_status_code,
		int pm_os_error_no)
{
    struct sigaction sa;

    message_info.mtype  = 1;
    MD.message_id       = LOGGER_MSG;
    MD.message_sub_id   = DEFAULT_SUB_MSG;
    strcpy(MD.process_no,   MY_PROCESS_NO);
    strcpy(MD.process_name, MY_PROCESS_NAME);
    MD.os_process_no    = getpid();
    MD.trans_time       = time(NULL);
    MD.select_flag[0]   = '0';
    MD.status_code      = pm_status_code;
    MD.os_error_no      = pm_os_error_no;
    MD.msg_length       = pm_send_length;

    memcpy(MD.message, pm_send_buffer, pm_send_length);
	
    /* Logger 존재여부를 확인 */
    if (LOGGER_SHM_INDEX < 0) return -1;

    if (kill(shm_info->process_info[LOGGER_SHM_INDEX].os_process_no, 0) == 0)
    {
        if (pm_wait_sec < 0) pm_wait_sec = 0;
        sa.sa_handler = timeout;
        sigaction(SIGALRM, &sa, NULL);
        alarm(pm_wait_sec);
		if(msgsnd(LOGGER_Q_ID, (char *)&message_info, pm_send_length+MSG_HEADER_LEN, 0) != 0)
        {
            alarm(0);
            return -1;
        }
        alarm(0);
        return pm_send_length;
    }
    return -1;
}

/*
 * 자기 GROUP에 전송 Module
 */
int     ml_sub_send_group (int    pm_msg_type,
                           int    pm_msg_sub_type,
                           char * pm_send_buffer,
                           int    pm_send_length,
                           int    pm_q_mtype,
                           int    pm_wait_sec)
{
    struct      sigaction sa;

    message_info.mtype  = pm_q_mtype;
        
    MD.message_id       = pm_msg_type;
    MD.message_sub_id   = pm_msg_sub_type;

    strcpy(MD.process_no,   MY_PROCESS_NO);
    strcpy(MD.process_name, MY_PROCESS_NAME);
    MD.os_process_no    = getpid();
    MD.trans_time       = time(NULL);
    MD.select_flag[0]   = '0';
    MD.status_code      = 0;
    MD.os_error_no      = 0;
    MD.msg_length       = pm_send_length;
    memcpy(MD.message, pm_send_buffer, pm_send_length);

    if (pm_wait_sec < 0) pm_wait_sec = 0;

    if (MAX_SEND_GROUP < 0) return -9;

    SEND_GROUP_INDEX = LAST_SEND_GROUP_INDEX;
    SEND_GROUP_INDEX ++;

    if (SEND_GROUP_INDEX > MAX_SEND_GROUP) SEND_GROUP_INDEX = 0;
    while (SEND_GROUP_INDEX != LAST_SEND_GROUP_INDEX)
    {
        if (kill(shm_info->process_info[SEND_GROUP_SHM_INDEX[SEND_GROUP_INDEX]].os_process_no, 0) == 0)
        {
            sa.sa_handler = timeout;
            sigaction(SIGALRM, &sa, NULL);
            alarm(pm_wait_sec);
            if (msgsnd(SEND_GROUP_Q_ID[SEND_GROUP_INDEX], (char *)&message_info,
                       pm_send_length + MSG_HEADER_LEN, 0) != 0)
            {
                alarm(0);
                return -1;
            }
            alarm(0);
            return (pm_send_length);
        }
        SEND_GROUP_INDEX ++;
        if (SEND_GROUP_INDEX > MAX_SEND_GROUP) SEND_GROUP_INDEX = 0;
    }
    LAST_SEND_GROUP_INDEX = SEND_GROUP_INDEX;
    return -1;
}

/*
 * 자기 GROUP중에서 특정 Process에게 전송 Module
 */
int     ml_sub_send_process(int    pm_process_no,
                            int    pm_msg_type,
                            int    pm_msg_sub_type,
                            char * pm_send_buffer,
                            int    pm_send_length,
                            int    pm_q_mtype,
                            int    pm_wait_sec)
{
    int         loop_idx;
    struct      sigaction sa;

    message_info.mtype  = pm_q_mtype;
        
    MD.message_id       = pm_msg_type;
    MD.message_sub_id   = pm_msg_sub_type;

    strcpy(MD.process_no,   MY_PROCESS_NO);
    strcpy(MD.process_name, MY_PROCESS_NAME);
    MD.os_process_no    = getpid();
    MD.trans_time       = time(NULL);
    MD.select_flag[0]   = '0';
    MD.status_code      = 0;
    MD.os_error_no      = 0;
    MD.msg_length       = pm_send_length;
    memcpy(MD.message, pm_send_buffer, pm_send_length);

    if (pm_wait_sec < 0) pm_wait_sec = 0;

    if (MAX_SEND_GROUP < 0) return -9;

    for (loop_idx = 0; loop_idx <= MAX_SEND_GROUP; loop_idx ++)
    {
        if (atoi(shm_info->process_info[SEND_GROUP_SHM_INDEX[loop_idx]].process_no) == pm_process_no)
            break;
    }
    if (loop_idx > MAX_SEND_GROUP) return -8;
    if (kill(shm_info->process_info[SEND_GROUP_SHM_INDEX[loop_idx]].os_process_no, 0) != 0)
        return -7;

    sa.sa_handler = timeout;
    sigaction(SIGALRM, &sa, NULL);
    alarm(pm_wait_sec);
    if (msgsnd(SEND_GROUP_Q_ID[loop_idx], (char *)&message_info, pm_send_length+MSG_HEADER_LEN, 0) != 0)
    {
        alarm(0);
        return -1;
    }
    alarm(0);
    return (pm_send_length);
} /* ml_sub_send_process */

/*
 * 수신 Module
 */
int ml_sub_recv_all (char *pm_recv_buffer, int pm_buffer_length, int pm_wait_sec)
{
    int rlen = 0 ;
    struct sigaction sa;

    if (pm_wait_sec < 0) pm_wait_sec = 0;

    timeout_flag = 0;
    sa.sa_handler = timeout;
    sigaction(SIGALRM, &sa, NULL);
    alarm(pm_wait_sec);
    rlen = msgrcv(MY_Q_ID, pm_recv_buffer, pm_buffer_length, 0L, 0);
    alarm(0);
    return (rlen);
}

/*
 * 수신 Module
 */
int     ml_sub_recv_select (int    pm_mtype,
                            char * pm_recv_buffer,
                            int    pm_buffer_length,
                            int    pm_wait_sec)
{
    int         rlen = 0;
    struct      sigaction sa;

    if (pm_wait_sec < 0) pm_wait_sec = 0;

    timeout_flag = 0;
    sa.sa_handler = timeout;
    sigaction(SIGALRM, &sa, NULL);
    alarm(pm_wait_sec);
    rlen = msgrcv(MY_Q_ID, pm_recv_buffer, pm_buffer_length, (long)pm_mtype, 0);
    alarm(0);
    return (rlen);
}

/*
 * 종료 Module
 */
int ml_sub_end(void)
{
	ml_sub_send_moni((char*)"ml_sub_end #1", strlen("ml_sub_end #1"), 3, 0, 0);
    shm_info->process_info[MY_SHM_INDEX].process_status = INACTIVE_STATUS;
	ml_sub_send_moni((char*)"ml_sub_end #2", strlen("ml_sub_end #1"), 3, 0, 0);
    shm_info->process_info[MY_SHM_INDEX].stop_time      = time(NULL);
	ml_sub_send_moni((char*)"ml_sub_end #3", strlen("ml_sub_end #1"), 3, 0, 0);
    shmdt((void *)0);
	ml_sub_send_moni((char*)"ml_sub_end #4", strlen("ml_sub_end #1"), 3, 0, 0);

    return 0;
}

/*
 * -------------------------------------------------------------------------- *
 *                                 SUBROUTINES                                *
 * -------------------------------------------------------------------------- *
 */

/*
 * 프로세스 이름을 KERNEL Format Buffer에서 얻는다.
 */
static inline void get_process_name(char *dst, char * process_name)
{
#if 0
    int         i;

    for (i = 0; i < 64; i ++)
    {
        if (dst[i+PROCESS_NAME_OFFSET] <= ' ') break;
        process_name[i] = dst[i+PROCESS_NAME_OFFSET];
    }
    process_name[i] = '\0';
#else
	/* 
	 * ps -ef 실행후 8번째 CMD 값 얻어옴
	 * UID        PID  PPID  C STIME TTY          TIME CMD
	 */
    sscanf(dst, "%*s %*s %*s %*s %*s %*s %*s %s", process_name);
#endif
}

/*
 * 프로세스 ID를 KERNEL Format Buffer에서 얻는다.
 */
static int get_process_id(char *dst)
{
    static char process_id[7];
    memcpy(process_id, &dst[9], 6);
    process_id[6] = '\0';
    return atoi(process_id);
}

/*
 * 숫자를 역순의 문자열로 전환한다.
 */
static char * itoa_reverse(int id)
{
    static char ret[32];
    int         idx = 0;

    memset(ret,0x00,sizeof(ret));
    while (id)
    {
        ret[idx] = '0' + (id % 10);
        id /= 10;
        idx ++;
    }
    return ret;
}

static void safe_strcpy(char *dst, char *src)
{
    if (src[0])
        strcpy(dst, src);
    else
        dst[0] = '\0';
}

static void timeout(int signo)
{
    timeout_flag = 1;
}

/* END */

int sndCheck2(char* process_no,int flag)
{
    int         ret;
    char        Process_no[11];    
    struct      msqid_ds msgctlbuf;    
    _CheckQ     CheckQ;

    msgctl(CHECK_Q_ID, IPC_STAT, &msgctlbuf);	
    if ( msgctlbuf.msg_qnum > 15 )
        return -1;
    
    memset(Process_no,0x00,sizeof(Process_no));  
    sprintf(Process_no,"%s",process_no);

	CheckQ.mesg_type = 10L;
	memset(CheckQ.body.ProcessNo ,0x00, sizeof(CheckQ.body.ProcessNo ));
    memcpy(CheckQ.body.ProcessNo ,Process_no,sizeof(CheckQ.body.ProcessNo));
    CheckQ.body.ProcessCheckFlag = flag;
  	
    ret = msgsnd(CHECK_Q_ID, (_CheckQ*)&CheckQ, sizeof(CheckQ.body), IPC_NOWAIT|MSG_NOERROR);
    if ( ret != 0 )
    {
        printf("process_no [%s]\n", Process_no);
        ml_sub_end();
        return -1;
    }

    return 1;
}

int strReplace(char* sourceMsg , char* a , char* b)
{
	int cnt;
	char* pstrtmp;
	char msgtmp[512];
	pstrtmp = (char*)strstr(sourceMsg,a);
	while(pstrtmp)
	{
		memset(msgtmp,0x00,sizeof(msgtmp));
		memcpy(msgtmp,sourceMsg,(cnt = (int)(pstrtmp - sourceMsg)));
		memcpy(msgtmp+cnt,b,strlen(b));
		memcpy(msgtmp+cnt+strlen(b),pstrtmp+strlen(a),strlen(pstrtmp+strlen(a)));		
		strcpy(sourceMsg,msgtmp);
		pstrtmp = (char*)strstr(pstrtmp+strlen(b),a);
	}
	return 1;
}

int Alert2Admin(char* pForm,...)
{
	int sockfd, len;
	struct sockaddr_in serv_addr;
	struct timeval tv;

	char strParam[256];
	char transBuf[350];

	va_list pArg;
	va_start(pArg, pForm);
	vsprintf(strParam, pForm, pArg);
	va_end(pArg);

    strReplace(strParam,(char*)" ",(char*)"%20");
	memset(transBuf,0x00,sizeof(transBuf));
	sprintf(transBuf,"get %s?%s\n",WEB_PAGE,strParam);
	printf("\nAlert2Admin:[%s]\n",transBuf);

	memset((char*)&serv_addr,0x00,sizeof(serv_addr));
	serv_addr.sin_family = AF_INET;
	serv_addr.sin_addr.s_addr = inet_addr(CALL_WEB);
	serv_addr.sin_port = htons(PORT_WEB);

	if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
		return -1;
	}
	
	tv.tv_sec = 5;
	tv.tv_usec = 0;
	
	if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
	}

	if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
	    return -1;
	}
	
	if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
		return -1;
	}
	len = write(sockfd,transBuf,strlen(transBuf));
	if( len < 0 )
	    return -1;
	memset(transBuf,0x00,sizeof(transBuf));
	len = read(sockfd,transBuf,sizeof(transBuf));
	close(sockfd);
	if (strncmp(transBuf,"OK",2) != 0 )
	    return -1;
	printf("\n%s\n",transBuf);
	return 0;
}
