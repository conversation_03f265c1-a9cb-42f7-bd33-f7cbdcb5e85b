/*
 * Program Name   : create_table.c
 * Remarks        : Make .table File
 * Initial Coding : 97.07.17 InMok,Song
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <memory.h>
#include <time.h>

char iBuff[1024];

FILE	*iFD_tin, *oFD;
char	iFILE_dd[256], iFILE_tin[256], oFILE[256];
char	dd_table_name[60][32],	tin_table_name [60][ 32];
char	dd_table_type[60],	tin_table_value[60][256];
int	dd_table_size[60];
int	dd_table_count,		tin_table_count;

void	init_rtn(int, char **);
void	get_dd_info(FILE *);
void	create_table(void);
void	make_table(void);
void	GetStringByLen(int, char *, int);

int main (int argc, char *argv[])
{
	init_rtn(argc, argv);
	create_table();
	exit(0);
}

void init_rtn(int argc, char *argv[])
{
	char iFILE_dd[256];
	FILE *iFD_dd;

	if (argc < 2)
	{
		printf("There is not input file name.\n");
		exit(10);
	}

	strcpy(iFILE_dd,  argv[1]);
	strcat(iFILE_dd,  ".dd");
	if ((iFD_dd = fopen(iFILE_dd, "r")) == NULL)
	{
		printf("%s fopen error.\n", iFILE_dd);
		exit(1);
	}
	get_dd_info(iFD_dd);
	fclose(iFD_dd);

	strcpy(iFILE_tin, argv[1]);
	strcat(iFILE_tin, ".tin");
	if ((iFD_tin = fopen(iFILE_tin, "r")) == NULL)
	{
		printf("%s fopen error.\n", iFILE_tin);
		exit(2);
	}
	strcpy(oFILE,     argv[1]);
	strcat(oFILE,     ".table");
	if ((oFD = fopen(oFILE, "w")) == NULL)
	{
		printf("%s fopen error.\n", oFILE);
		exit(3);
	}
}

void get_dd_info(FILE *fd)
{
	int i, j;
	char temp[256];

	dd_table_count = 0;
	while (fgets(iBuff, sizeof(iBuff), fd) != NULL)
	{
		if (iBuff[0] != ' ') continue;
		memset(temp,0x00,sizeof(temp));
		for(j=0,i=1;iBuff[i];i++)
		{
			if (iBuff[i] == ' ') { i++; break;}
			temp[j++] = iBuff[i];
		}
		memcpy(dd_table_name[dd_table_count], temp, sizeof(temp));
		for(;iBuff[i]; i++) if (iBuff[i] != ' ') break;
		if (iBuff[i] != 'N' && iBuff[i] != 'A')
		{
			printf("Value Type is ONLY 'N' or 'A'\n");
			fputs(iBuff,stderr);
			exit(1);
		}
		dd_table_type[dd_table_count] = iBuff[i++];
		for(;iBuff[i]; i++) if (iBuff[i] != ' ') break;
		memset(temp,0x00,sizeof(temp));
		for(j=0;iBuff[i]!=';';i++)
		{
			if (iBuff[i] == ' ') continue;
			if (iBuff[i] < '0' || iBuff[i] > '9')
			{
				printf("Invalid Numeric TYPE(%c).\n",iBuff[i]);
				fputs(iBuff,stderr);
				exit(1);
			}
			temp[j++] = iBuff[i];
		}
		dd_table_size[dd_table_count] = atoi(temp);
		dd_table_count++;
	}
}

void create_table(void)
{
	int check = 0,i,j;
	char temp[256];

	tin_table_count = 0;
	while (fgets(iBuff, sizeof(iBuff), iFD_tin) != NULL)
	{
		if (memcmp(iBuff,"/=",2)!=0 && memcmp(iBuff," =",2)!=0)
			continue;
		if (memcmp(iBuff,"/=",2)==0)
		{
			if (check == 0) check = 1;
			else make_table();
			tin_table_count = 0;
		}

		/* GET VALUE NAME */
		memset(temp,0x00,sizeof(temp));
		for(i=2;iBuff[i];i++) if (iBuff[i] != ' ' && iBuff[i] != '\t') break;
		for(j=0;iBuff[i];i++)
		{
			if (iBuff[i] == ' ' || iBuff[i] == '\t') break;
			temp[j++] = iBuff[i];
		}
		strcpy(tin_table_name[tin_table_count], temp);

		/* GET VALUE */
		memset(temp,0x00,sizeof(temp));
		for(j=0;iBuff[i];i++) if (iBuff[i] != ' ' && iBuff[i] != '\t') break;
		for(j=0;iBuff[i];i++)
		{
			if (iBuff[i] == ' ' || iBuff[i] == '\t' || iBuff[i] == '\n') break;
			temp[j++] = iBuff[i];
		}
		strcpy(tin_table_value[tin_table_count],temp);
		tin_table_count++;
	}
	if (check) make_table();
}

void make_table(void)
{
	int i,j,k;
	char wBuf[256];

	for (i = 0; i < dd_table_count; i++)
	{
		memset(wBuf,0x00,sizeof(wBuf));
		for (j = 0; j < tin_table_count; j++)
		{
			if (strcmp(dd_table_name[i],tin_table_name[j])==0) break;
		}
		if (j >= tin_table_count)
		{
			if (dd_table_type[i] == 'N') memset(wBuf,'0',dd_table_size[i]);
		}
		else
		{
			if (dd_table_type[i] == 'A')
				strcpy(wBuf,tin_table_value[j]);
			else
			{
				for(k=0;tin_table_value[j][k];k++)
				{
					if (tin_table_value[j][k] < '0' || tin_table_value[j][k] > '9')
					{
						printf("Invalid Numeric .tin File.\n");
						printf("(%s %s)\n",tin_table_name[j],tin_table_value[j]);
						exit(1);
					}
				}
				GetStringByLen(atoi(tin_table_value[j]), wBuf, dd_table_size[i]);
			}
		}
		fwrite(wBuf,dd_table_size[i],1,oFD);
	}
}

void GetStringByLen(int src, char *dst, int limit)
{
	char temp[64];
	int  i = -1, sign, mod,len;
	sign = (src < 0) ? 1 : 0;
	while (1)
	{
		mod = src % 10;
		i++;
		temp[i] = 0x30 + (char)mod;
		src = src / 10;
		if (src == 0) break;
	}
	if (sign == 1)
	{
		i++;
		temp[i] = '-';
	}
	if (i >= limit) i = limit - 1;
	for (len = 0; len <= i; (len)++) dst[len] = temp[i-len];
}
