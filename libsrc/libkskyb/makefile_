CC = g++
CFLAGS = 


#all : qdata ksqueue kssocket ksthread  ksqueuetable
all : qdata ksqueue kssocket ksthread ksconfig

ksqueue : ksqueue.o
	ar rc lib/libksqueue.a obj/ksqueue.o


ksqueuetable : ksqueuetable.o
	ar rc lib/libksqueuetable.a obj/ksqueuetable.o
	
ksconfig : ksconfig.o
	ar rc lib/libksconfig.a obj/ksconfig.o



qdata : qdata.o
	ar rc lib/libqdata.a obj/qdata.o

kssocket : kssocket.o
	ar rc lib/libkssocket.a obj/kssocket.o

ksthread : ksthread.o
	ar rc lib/libksthread.a obj/ksthread.o

qdata.o : src/qdata.cpp
	rm -rf obj/qdata.o lib/libqdata.a
	$(CC) -fPIC -o obj/qdata.o -Iinc -c src/qdata.cpp


ksqueue.o : src/ksqueue.cpp  
	rm -rf obj/ksqueue.o lib/libksqueue.a 
	$(CC)  -fPIC -o obj/ksqueue.o -Iinc -c src/ksqueue.cpp


ksqueuetable.o : src/ksqueuetable.cpp  
	rm -rf obj/ksqueuetable.o lib/libksqueuetable.a 
	$(CC)  -fPIC -o obj/ksqueuetable.o -Iinc -c src/ksqueuetable.cpp

ksconfig.o : src/ksconfig.cpp  
	rm -rf obj/ksconfig.o lib/libksconfig.a 
	$(CC)  -fPIC -o obj/ksconfig.o -Iinc -c src/ksconfig.cpp




kssocket.o : src/kssocket.cpp
	rm -rf obj/kssocket.o lib/libkssocket.a
	$(CC)  -fPIC -o obj/kssocket.o -Iinc -c src/kssocket.cpp

ksthread.o : src/ksthread.cpp
	rm -rf obj/ksthread.o lib/libksthread.a
	$(CC)  -fPIC -o obj/ksthread.o -Iinc -c src/ksthread.cpp 


clean :
	rm -rf obj/ksqueue.o lib/libksqueue.a obj/kssocket.o lib/libkssocket.a obj/ksthread.o lib/libthread.a  obj/qdata.o lib/libqdata.a obj/ksconfig.o


install :
	cp lib/libksqueue.a ${HOME}/library
	cp inc/ksqueue.h ${HOME}/library
#	cp lib/libksqueuetable.a ${HOME}/library
	cp inc/ksqueuetable.h ${HOME}/library
	cp lib/libkssocket.a ${HOME}/library
	cp inc/kssocket.h ${HOME}/library
	cp lib/libksthread.a ${HOME}/library
	cp inc/ksthread.h ${HOME}/library
	cp lib/libqdata.a ${HOME}/library
	cp inc/qdata.h ${HOME}/library
	cp lib/libksconfig.a ${HOME}/library
	cp inc/ksconfig.h ${HOME}/library

