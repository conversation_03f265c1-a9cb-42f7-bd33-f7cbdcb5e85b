#ifndef _KQUEUE_H_
#define _KQUEUE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

struct KQueue{
    int pushIdx;
    int popIdx;
    int dataSize;
    int maxQueueCount;
    int entrySize;
    void* pQue;
//    RcvData2 data;
    int (*createKQueue)(struct KQueue* this, int maxQueueCount, int entrySize);
    int (*pushKQueue)(struct KQueue* this, void* entry);
    void* (*frontKQueue)(struct KQueue* this);
    int (*popKQueue)(struct KQueue* this);
    void (*freeKQueue)(struct KQueue* this);
    int (*sizeKQueue)(struct KQueue* this);
};

int createKQueue(struct KQueue* this, int maxQueueCount, int entrySize);
void freeKQueue(struct KQueue* this);
int pushKQueue(struct KQueue* this,void* entry);
void* frontKQueue(struct KQueue* this);
int popKQueue(struct KQueue* this);
void KQueue_int(struct KQueue* this);
int sizeKQueue(struct KQueue* this);


#endif

