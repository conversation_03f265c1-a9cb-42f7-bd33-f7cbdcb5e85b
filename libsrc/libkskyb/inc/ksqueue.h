#ifndef _KSKYB_QUEUE_H_
#define _KSKYB_QUEUE_H_

/* 
 * usage:
 *
 * KSQdata data;
 * CKSQueue queue;
 *
 * queue.push(data);
 * queue.front(data);
 * queue.pop();
 *
 *
 *
 * log
 *
 * queue.log_to(logfunc);
 * _log("asdf");
 * _log("asdf [%s]",logMsg);
 *
 *
 *
 */


#include <queue>
#include <string>
#include <semaphore.h>
#include <errno.h>
#include "qdata.h"
/* #include <list>
#include <deque>
*/

using namespace std;


/* typedef struct _tag_KSQdata {
    int key;
    char msg[1024];
} KSQdata;
*/
/*
namespace kskyb {
    class KSQdata{
        public:
            KSQdata(string msg, int priority=0,int time=0){
                m_msg      = msg; 
                m_priority = priority;         
                m_time = time;    
            }

            string m_msg;
            int    m_priority;
            int    m_time;

    };
*/

/*
    ostream& operator<<( ostream& os , const KSQdata& event) {
        return os << event.m_priority << ":" << event.m_time << "  " << event.m_msg << endl;
    }

    */


//static void (*_log_func)(const char*) = 0;
class CKSQueue
{
    public:
        CKSQueue();
        virtual ~CKSQueue();
        int pop();
        int pop_r();
        KSQdata front();
        KSQdata front_r();
        int push(KSQdata data);
        int push_r(KSQdata data);
        int getCount();
        void log_to(void(*func)(const char*));
    private:
//      queue<KSQdata, list<KSQdata> > m_queue;
        priority_queue<KSQdata, vector<KSQdata>, less<KSQdata> > m_queue;
        sem_t m_sem;
//        void _log(const char *format, ... ) ;
};

#endif

