#ifndef _KSKYB_QDATA_H_
#define _KSKYB_QDATA_H_
#include <iostream>
#include <string>
//#include <cstdlib>
//#include <string.h>
/* #include <list>
#include <deque>
*/
using namespace std;
class KSQdata{
    public:
	KSQdata(string msg, int priority=0,int time=0);
        /*
	KSQdata(string msg, int priority=0,int time=0){
	    m_msg      = msg; 
	    m_priority = priority;         
	    m_time = time;    
	}
        */

        string m_msg;
	int    m_priority;
	int    m_time;
};

#endif

