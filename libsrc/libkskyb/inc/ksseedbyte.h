#ifndef _KSKYB_SEED_BYTE_
#define _KSKYB_SEED_BTYE_
#include "seedx.h"

class CSeedByte {
    enum { SEED_DATA_SIZE=16};
    public:
        CSeedByte();
        ~CSeedByte();
        // data size : 16 byte  이내
        // result size fixed : 16 byte
        void encrypt(void* data, void* result);
        // data size : fixed : 16 byte
        // result size : 16 byte 이내
        void decrypt(void* data, void* result);

        // key size fixed : 16 byte
        void setUserKey(void* key);
    private:
        BYTE pbUserKey[16];        
        DWORD pdwRoundKey[32];
};


#endif


/*
 *  usage:
 *
    CSeedByte seedByte;
    char szData[16];
    char szResult[16];

    BYTE pbUserKey[16] = {0x4b, 0x4e, 0x42, 0x41, 0x4e, 0x4b, 0x4b, 0x4e, 0x42, 0x41, 0x4e, 0x4b, 0x4b, 0x4e, 0x42, 0x41};

    memset(szData,0x00,sizeof(szData));
    memset(szResult,0x00,sizeof(szResult));

    strcpy(szData,"01198973574");
    seedByte.setUserKey(pbUserKey);
    
    seedByte.encrypt(szData,szResult);
    memset(szData,0x00,sizeof(szData));
    seedByte.decrypt(szResult,szData);
    printf("[%s]\n",szData);

 */


