#include <iostream>
#include "ksqueue.h"
//#include "qdata.h"

#ifdef UNIX
    #include <sys/varargs.h>
#else
    #include <stdarg.h>
#endif

bool operator<( const KSQdata& a, const KSQdata& b) {

    if( a.m_priority < b.m_priority )
        return false;
    if( a.m_priority > b.m_priority )
        return true;

    if( a.m_time < b.m_time )
        return false;
    if( a.m_time > b.m_time )
        return true;

    return true;


}


CKSQueue::CKSQueue()
{
    sem_init(&m_sem,1,1);
}

CKSQueue::~CKSQueue()
{
    sem_destroy(&m_sem);
}


KSQdata CKSQueue::front()
{

    if(m_queue.empty() )
    {
        return KSQdata("",-1,-1);
    }

    return  m_queue.top();
}


KSQdata CKSQueue::front_r()
{
//    int errno;
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        {
            return KSQdata("",-1,-1);
        }


    if(m_queue.empty() )
    {
    if( sem_post(&m_sem) == -1 )
        return KSQdata("",-1,-1);


        return KSQdata("",-1,-1);
    }

    if( sem_post(&m_sem) == -1 )
        return KSQdata("",-1,-1);


    return  m_queue.top();
}

int CKSQueue::push(KSQdata data)
{
    m_queue.push(data);
    return 0;
}

int CKSQueue::push_r(KSQdata data)
{
//   int errno;
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        {
            return -1;
        }
    m_queue.push(data);

    if( sem_post(&m_sem) == -1 )
        return 1;
    return 0;
}



int CKSQueue::pop()
{
    m_queue.pop();
    return 0;
}


int CKSQueue::pop_r()
{
//    int errno;
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        {
            return -1;
        }

    m_queue.pop();
    if( sem_post(&m_sem) == -1 )
        return 1;

    return 0;
}



int CKSQueue::getCount()
{
    return m_queue.size();
}


