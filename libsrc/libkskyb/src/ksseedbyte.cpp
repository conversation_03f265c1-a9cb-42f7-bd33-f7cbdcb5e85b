#include "ksseedbyte.h"

CSeedByte::CSeedByte()
{
    memset(pbUserKey,0x00,sizeof(pbUserKey));
}

CSeedByte::~CSeedByte()
{
}

void CSeedByte::encrypt(void* data, void* result)
{
    BYTE* pbData = (BYTE*)data;

    /* Key Schedule Algorithm */
    SeedEncRoundKey(pdwRoundKey, pbUserKey);

    /* Encryption Algorithm */
    SeedEncrypt(pbData, pdwRoundKey);

    memcpy(result,pbData,SEED_DATA_SIZE);

    return;
}

void CSeedByte::decrypt(void* data, void* result)
{
    BYTE* pbData = (BYTE*)data;

    /* Key Schedule Algorithm */
    SeedEncRoundKey(pdwRoundKey, pbUserKey);

    /* Decryption Algorithm */
    SeedDecrypt(pbData, pdwRoundKey);

    memcpy(result,pbData,SEED_DATA_SIZE);

    return;
}


void CSeedByte::setUserKey(void* key)
{
    memcpy(pbUserKey,key,sizeof(pbUserKey));
    return;
}

