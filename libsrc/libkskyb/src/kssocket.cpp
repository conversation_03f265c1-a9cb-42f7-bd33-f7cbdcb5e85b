#include "kssocket.h"
#include <time.h>
CKSSocket::CKSSocket()
{
}

CKSSocket::~CKSSocket()
{

}

int CKSSocket::connectDomain(char* path)
{
    int nSockOpt = 1;
    struct sockaddr_un cli_addr;

    if ( (m_sockfd=socket(AF_UNIX,SOCK_STREAM,0))<0 ) {
	return -1; 
    }

   if ( setsockopt(m_sockfd,SOL_SOCKET,SO_KEEPALIVE,(char*)&nSockOpt,sizeof(nSockOpt))<0 ) {
        this->close();
	return -1; 
    }       

    memset(&cli_addr,0x00,sizeof(cli_addr));

    cli_addr.sun_family = AF_UNIX;
    strcpy(cli_addr.sun_path,path);
    clilen = sizeof(cli_addr);

    if( connect(m_sockfd,(struct sockaddr*)&cli_addr,clilen) < 0 )
    {
        this->close();
	return -2;
    }

    return 0;
}

/** 
 * @brief domain socket 을 생성한다 넌블러킹모드
 * @return Succ 0 Fail errno
 */
int CKSSocket::createDomainNon(char* path)
{
    int nSockOpt = 1;
    int nSndbuf = 49152;
    int nRcvbuf = 49152;
    struct sockaddr_un serv_addr;

    int flags;
    struct timeval tv;


    if( access(path,F_OK) == 0 )
    {
        unlink(path);
    }

    if ( (m_sockfd=socket(AF_UNIX,SOCK_STREAM,0))<0 ) {
	return errno; 
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_REUSEADDR,(char*)&nSockOpt,sizeof(nSockOpt))<0 ) {
	return errno; 
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_KEEPALIVE,(char*)&nSockOpt,sizeof(nSockOpt))<0 ) {
	return errno; 
    }       

    memset(&serv_addr,0x00,sizeof(serv_addr));

    serv_addr.sun_family = AF_UNIX;
    strcpy(serv_addr.sun_path,path);

    if ( bind(m_sockfd,(struct sockaddr*)&serv_addr,sizeof(serv_addr))<0 ) {
	return errno;
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&nSndbuf,sizeof(nSndbuf))<0 ) {
	return errno;
    }
    if( setsockopt(m_sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&nRcvbuf,sizeof(nRcvbuf))<0 ) {
	return errno;
    }

    tv.tv_sec = 5;
    tv.tv_usec = 0;
    if (setsockopt(m_sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv))<0) {
        return errno;
    }
    if (setsockopt(m_sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv))<0) {
        return errno;
    }


    if ((flags = fcntl(m_sockfd,F_GETFL,0))<0) return errno;
    flags |= O_NONBLOCK;
    if (fcntl(m_sockfd,F_SETFL, flags)<0) return errno;





    if ( listen(m_sockfd,10)<0 ) {
        return errno;
    }

    /** @brief multiplexing */
    //    FD_ZERO(&readfds);
    //    FD_SET(m_sockfd,&readfds);
    //    fd_max = m_sockfd;

    return 0;
}


/** 
 * @brief domain socket 을 생성한다
 * @return Succ 0 Fail errno
 */
int CKSSocket::createDomain(char* path)
{
    int nSockOpt = 1;
    int nSndbuf = 49152;
    int nRcvbuf = 49152;
    struct sockaddr_un serv_addr;

    if( access(path,F_OK) == 0 )
    {
        unlink(path);
    }

    if ( (m_sockfd=socket(AF_UNIX,SOCK_STREAM,0))<0 ) {
        return errno; 
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_REUSEADDR,(char*)&nSockOpt,sizeof(nSockOpt))<0 ) {
        return errno; 
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_KEEPALIVE,(char*)&nSockOpt,sizeof(nSockOpt))<0 ) {
        return errno; 
    }       

    memset(&serv_addr,0x00,sizeof(serv_addr));

    serv_addr.sun_family = AF_UNIX;
    strcpy(serv_addr.sun_path,path);

    if ( bind(m_sockfd,(struct sockaddr*)&serv_addr,sizeof(serv_addr))<0 ) {
        return errno;
    }
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&nSndbuf,sizeof(nSndbuf))<0 ) {
        return errno;
    }
    if( setsockopt(m_sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&nRcvbuf,sizeof(nRcvbuf))<0 ) {
        return errno;
    }

    if ( listen(m_sockfd,10)<0 ) {
        return errno;
    }

    /** @brief multiplexing */
    //    FD_ZERO(&readfds);
    //    FD_SET(m_sockfd,&readfds);
    //    fd_max = m_sockfd;

    return 0;
}

int CKSSocket::accept2()
{
    int ret;
    clilen = sizeof(m_cliaddr);
    memset(&m_cliaddr,0x00,sizeof(m_cliaddr));
    ret = this->select(0,10000);
    if( ret == 0 )
    {
       return 0;
    }
    if( ret < 0)
    {
        printf("------- to alert chozo99 accept [%d][%s]",
                ret,strerror(errno));
        fflush(stdout);
        return ret;
    }
    return ::accept(m_sockfd,(struct sockaddr *)&m_cliaddr,(socklen_t*)&clilen);

}


int CKSSocket::accept()
{
    int ret;
    clilen = sizeof(m_cliaddr);
    memset(&m_cliaddr,0x00,sizeof(m_cliaddr));
    return ::accept(m_sockfd,(struct sockaddr *)&m_cliaddr,(socklen_t*)&clilen);

}


int CKSSocket::accept_in()
{
    clilen = sizeof(cli_inaddr);
    memset(&cli_inaddr,0x00,sizeof(cli_inaddr));
    return ::accept(m_sockfd,(struct sockaddr *)&cli_inaddr,(socklen_t*)&clilen);
}


char* CKSSocket::getPeerIP()
{
    return inet_ntoa(cli_inaddr.sin_addr);
}

int CKSSocket::send(char* buff,int len)
{
    return write(m_sockfd,buff,len);
}

int CKSSocket::recv(char* buff,int len)
{
    return read(m_sockfd,buff,len);
}

int CKSSocket::close()
{
    return ::close(m_sockfd);
}

int CKSSocket::attach(int sockfd)
{

    int nSndbuf = 49152;
    int nRcvbuf = 49152;
 
    m_sockfd = sockfd;
    if ( setsockopt(m_sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&nSndbuf,sizeof(nSndbuf))<0 ) {
        printf("[%s]\n",strerror(errno));
//        return errno;
    }
    if( setsockopt(m_sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&nRcvbuf,sizeof(nRcvbuf))<0 ) {
        printf("[%s]\n",strerror(errno));
//        return errno;
    }

    return 0;
}

int CKSSocket::select()
{
    return select(3,0);
}

int CKSSocket::select(int usec)
{
    return select(0,usec);
}

int CKSSocket::select(int sec,int usec)
{
    fd_set readfds;
    struct timeval timeout;

    FD_ZERO(&readfds);
    FD_SET(m_sockfd,&readfds);
    timeout.tv_sec = sec;
    timeout.tv_usec = usec;
    
    return ::select(m_sockfd+1,&readfds,NULL,NULL,&timeout);
}

int CKSSocket::rcvmsg(char* buff)
{
    int ret;

    memset(szErrorMsg,0x00,sizeof(szErrorMsg));
/* recv queue data */
    ret = this->select();
    if( ret == 0 ) return 0;
    //ret = this->recv(buff,4096);
    ret = this->recv(buff,8192);
    if( ret == 0 )
    {
        sprintf(szErrorMsg,"close by peer");
        return -1;
    }
    if( ret < 0 ) {
        sprintf(szErrorMsg,"%s",strerror(errno));
//        this->close();
//        cout << " close by peer" << endl;
    }
    return ret;
/* recv queue data */
}

int CKSSocket::recvAllMsg(int sec)
{
    int ret; /* 함수 리턴값 */
    int len; /* 받은 길이 */
    int total=0; /* 받은 길이 합 */
    char buff[16384];
    this->packet="";
    this->packet.reserve(0);

    memset(szErrorMsg,0x00,sizeof(szErrorMsg));
    
    if( sec == 3 ) {
        ret = this->select(0,1000);
        if( ret == 0 )
        {
            return 0;
        }
    }

    /* recv queue data */
    while(1) {
        ret = this->select(sec, 0);
        if( ret == 0 ) break;

        memset(buff,0x00,sizeof(buff));
        errno = 0;
        len = this->recv(buff,4092);
        if( len == 0 )
        {
            sprintf(szErrorMsg,"close by peer");
            return -1;
        }
        if( len < 0 ) {
            sprintf(szErrorMsg,"%s",strerror(errno));
            return len;
            //        this->close();
            //        cout << " close by peer" << endl;
        }

        this->packet += buff;
        total+=len;
        //if( strstr(buff,"\r\nEND\r\n") )
        if( strstr(this->packet.c_str(),"\r\nEND\r\n") )
        {
            break;
        }
		//printf("%s\n", buff);
    }
	//printf("Buff:\n[%s]\n",  this->packet.c_str()); 


    return total;
/* recv queue data */
}

int CKSSocket::getSockfd()
{
    return m_sockfd;
}

char* CKSSocket::getErrMsg()
{
    return szErrorMsg;
}

const char* CKSSocket::getMsg()
{
    return this->packet.c_str();
}

