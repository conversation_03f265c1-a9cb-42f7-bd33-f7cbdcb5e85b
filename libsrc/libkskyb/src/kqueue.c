#include "kqueue.h"

void KQueue_int(struct KQueue* this) {
    this->createKQueue = createKQueue;
    this->pushKQueue = pushKQueue;
    this->freeKQueue = freeKQueue;
    this->frontKQueue = frontKQueue;
    this->popKQueue = popKQueue;
    this->sizeKQueue = sizeKQueue;
    this->pushIdx = 0;
    this->popIdx = 0;
    this->dataSize = 0;
}

int pushKQueue(struct KQueue* this,void* entry)
{
   if( this->dataSize >= this->maxQueueCount) return -1; 
    memcpy(
            this->pQue+( ((int)this->entrySize * (int)this->pushIdx) )
            ,entry,
            (int)this->entrySize);

    this->pushIdx = (int)this->pushIdx + 1;
    if( this->pushIdx == this->maxQueueCount ) {
        this->pushIdx = 0;
    }
    this->dataSize++;

    return 0;
}

void* frontKQueue(struct KQueue* this)
{
    return this->pQue + ( (int)this->entrySize * (int)this->popIdx ) ;
}

int popKQueue(struct KQueue* this)
{
    memset(this->pQue + ( this->entrySize * this->popIdx) , 0x00, this->entrySize);

    this->popIdx = (int)this->popIdx + 1;
    if( this->popIdx == this->maxQueueCount ) this->popIdx =0;
    this->dataSize--;

    return 0;
}

int createKQueue(struct KQueue* this, int maxQueueCount, int entrySize)
{
    this->maxQueueCount = maxQueueCount;
    this->entrySize = entrySize;
    this->pQue = malloc(entrySize * maxQueueCount);
}

void freeKQueue(struct KQueue* this)
{
    free(this->pQue);
}

int sizeKQueue(struct KQueue* this)
{
    return this->dataSize;
}

