#include "ksqueuetable.h"

CKSQueueTable::CKSQueueTable() 
{
}

CKSQueueTable::~CKSQueueTable()
{
}

int CKSQueueTable::getQueue(char *buff)
{
    char szType[8];
    char szAct[4];
    string strType;

/* check queue table */
        memset(szType,0x00,sizeof(szType));
        memcpy(szType,buff,6);
        memset(szAct,0x00,sizeof(szAct));
        memcpy(szAct,buff+6,3);

        strType = szType;

//
//
//        k = QueueMap.find(strType);
//        if( k == QueueMap.end() )
//        {   
//            // 없다 새로 생성
//            MapData.pqueue = new CKSQueue;
//            MapData.pqueue->log_to(logfunc);
//            pair<string,QueueMapData> NewItem(strType,MapData);
//            QueueMap.insert(NewItem);
//            cout << "queue create " << strType << endl;
//            // k 에 생성한 맵의 정보를 넣는다.
//            k = QueueMap.find(strType);
//            if( k == QueueMap.end() )
//            {   
//                cout << "map create fail ... " << endl;
//                return NULL;
//            }  
//        } else {
//            // 있다. 매칭
//            cout << "type = "  << k->first << endl;
//            MapData.pqueue = k->second.pqueue;
//        }
/* check queue table */



    return 0;
}




