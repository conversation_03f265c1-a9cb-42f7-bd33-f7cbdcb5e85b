#include "qdata.h"
#include <iostream>
#include <string>

KSQdata::KSQdata(string msg, int priority,int time){
	    m_msg      = msg; 
	    m_priority = priority;         
	    m_time = time;    
}

/* bool operator<( const KSQdata& a, const KSQdata& b) {

    if( a.m_priority < b.m_priority )
	return false;
    if( a.m_priority > b.m_priority )
	return true;

    if( a.m_time < b.m_time )
	return false;
    if( a.m_time > b.m_time )
	return true;

    return true;


}

ostream& operator<<( ostream& os , const KSQdata& event) {
    return os << event.m_priority << ":" << event.m_time << "  " << event.m_msg << endl;
}




*/




