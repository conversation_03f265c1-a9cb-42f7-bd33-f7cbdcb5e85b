#include "kscommon.h"

void wait_a_moment(int interval)
{
    wait_a_moment(0,interval);
}

void wait_a_moment(int sec,int usec)
{
    struct timeval subtime;
    subtime.tv_sec = sec;
    subtime.tv_usec = usec;
    select(0,(fd_set*)0,(fd_set*)0,(fd_set*)0,&subtime);
}

char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

