CC = g++
CFLAGS = 


#all : qdata ksqueue kssocket ksthread  ksqueuetable
all : qdata ksqueue kssocket ksthread ksbase64 ksconfig kscommon kqueue ksseedbyte


ksseedbyte : obj/ksseedbyte.o obj/Seedx-1.o
	        ar rc lib/libksseedbyte.a $^

ksqueue : ksqueue.o
	ar rc lib/libksqueue.a obj/ksqueue.o
kqueue : kqueue.o
	ar rc lib/libkqueue.a obj/kqueue.o

kscommon : kscommon.o
	ar rc lib/libkscommon.a obj/kscommon.o



ksconfig : ksconfig.o
	ar rc lib/libksconfig.a obj/ksconfig.o




ksqueuetable : ksqueuetable.o
	ar rc lib/libksqueuetable.a obj/ksqueuetable.o

qdata : qdata.o
	ar rc lib/libqdata.a obj/qdata.o

kssocket : kssocket.o
	ar rc lib/libkssocket.a obj/kssocket.o


ksbase64 : ksbase64.o
	ar rc lib/libksbase64.a obj/ksbase64.o



ksthread : ksthread.o
	ar rc lib/libksthread.a obj/ksthread.o

qdata.o : src/qdata.cpp
	rm -rf obj/qdata.o lib/libqdata.a
	$(CC) -fPIC -o obj/qdata.o -Iinc -c src/qdata.cpp


ksqueue.o : src/ksqueue.cpp  
	rm -rf obj/ksqueue.o lib/libksqueue.a 
	$(CC)  -fPIC -o obj/ksqueue.o -Iinc -c src/ksqueue.cpp

kqueue.o : src/kqueue.c
	rm -rf obj/kqueue.o lib/libkqueue.a 
	gcc  -fPIC -o obj/kqueue.o -Iinc -c src/kqueue.c


kscommon.o : src/kscommon.cpp  
	rm -rf obj/kscommon.o lib/libkscommon.a 
	$(CC)  -fPIC -o obj/kscommon.o -Iinc -c src/kscommon.cpp




ksconfig.o : src/ksconfig.cpp
	rm -rf obj/ksconfig.o lib/libksconfig.a 
	$(CC)  -fPIC -o obj/ksconfig.o -Iinc -c src/ksconfig.cpp




ksbase64.o : src/ksbase64.cpp  
	rm -rf obj/ksbase64.o lib/libksbase64.a 
	$(CC)  -fPIC -o obj/ksbase64.o -Iinc -c src/ksbase64.cpp


ksqueuetable.o : src/ksqueuetable.cpp  
	rm -rf obj/ksqueuetable.o lib/libksqueuetable.a 
	$(CC)  -fPIC -o obj/ksqueuetable.o -Iinc -c src/ksqueuetable.cpp



kssocket.o : src/kssocket.cpp
	rm -rf obj/kssocket.o lib/libkssocket.a
	$(CC)  -fPIC -o obj/kssocket.o -Iinc -c src/kssocket.cpp

ksthread.o : src/ksthread.cpp
	rm -rf obj/ksthread.o lib/libksthread.a
	$(CC)  -fPIC -o obj/ksthread.o -Iinc -c src/ksthread.cpp 

obj/Seedx-1.o : src/Seedx-1.c
	rm -rf $@
	$(CC) -fPIC -o $@ -Iinc -c $^

obj/ksseedbyte.o : src/ksseedbyte.cpp
	rm -rf $@
	$(CC)  -fPIC -o $@ -Iinc -c $^


clean :
	rm -rf obj/*.o lib/*.a

install :
	cp  lib/libksqueue.a ${HOME}/library
	cp  lib/libkqueue.a ${HOME}/library
	cp  inc/ksqueue.h ${HOME}/library
	cp  inc/kqueue.h ${HOME}/library
#	cp  lib/libksqueuetable.a ${HOME}/library
	cp  inc/ksqueuetable.h ${HOME}/library
	cp  lib/libkssocket.a ${HOME}/library
	cp  inc/kssocket.h ${HOME}/library
	cp  lib/libksthread.a ${HOME}/library
	cp  inc/ksthread.h ${HOME}/library
	cp  lib/libqdata.a ${HOME}/library
	cp  inc/qdata.h ${HOME}/library
	cp  lib/libksbase64.a ${HOME}/library
	cp  inc/ksbase64.h ${HOME}/library
	cp  lib/libksconfig.a ${HOME}/library
	cp  inc/ksconfig.h ${HOME}/library
	cp  lib/libkscommon.a ${HOME}/library
	cp  inc/kscommon.h ${HOME}/library
	cp  lib/libksseedbyte.a ${HOME}/library
	cp  inc/ksseedbyte.h ${HOME}/library
	cp  inc/seedx.h ${HOME}/library
	cp  inc/seedx.tab ${HOME}/library


