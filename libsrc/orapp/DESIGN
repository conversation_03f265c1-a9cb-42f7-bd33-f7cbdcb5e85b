$Id: DESIGN,v 1.1.1.1 2007/05/07 00:24:44 neosms Exp $

ORAPP Library
Oracle OCI C++ Interface
Jordan Ritter <<EMAIL>>


Introduction

     This document summarizes the major features and changes to the
     ORAPP library, which is a complete rewrite-from-scratch of the
     current/old ORA++ library.

     The rewrite exposed numerous faults, inefficiencies and unhandled
     error scenarios.  As a result of these changes, the compiled size
     of the ORAPP library is 20% less than that of the previous
     incarnation (20k stripped vs. 26k stripped), is much more
     flexible as it has no external dependencies aside from the Oracle
     9i libs, is more flexible by way of providing more API calls and
     methods to manipulate data, and is academically (provably) faster
     since it eliminates several repeated API calls altogether by
     being more intelligent about caching information we need from
     Oracle.


Object Organization

     Everything is contained in the ORAPP namespace.

     The only directly-instantiable object is ORAPP::Connection;
     everything else uses protected constructors to enforce
     assumptions we make around who might be accessing what during
     whenever.

     ORAPP::Connection is the top-level object from which everything
     else derives or borrows.

          namespace ORAPP
            |
            -- class Connection
            |   |
            |   -- class Query
            |       |
            |       -- class Row
            |           |
            |           -- class Field
            |
            -- log-related function(s)
            |
            -- error-related function(s)


Noteworthy Changes

     As this is the second attempt at an Oracle OCI-based C++ access
     model, the chief differences bear pointing out:

       1. In the top-level Connection object, there are several
         strategies we could employ to manage all the various common
         OCI structures:

         i. define them as static and global, and access them whenever
            desired from anywhere.

         ii. define them as static but within a given namespace, and
            access them whenever desired from anywhere.

         iii. pass the pointers around as *'s, and use them in derived
            objects whenever desired.

         iv. pass the pointers around as **'s, and deref them in
            derived objects whenever desired.

         v. pass the pointers around as *&'s, and use them in derived
            objects whenever desired.

         The problem with ``i.'' is that it pollutes the global
         namespace and thus fails initial muster.

         ``ii.'' is a little better as it doesn't pollute the global
         namespace, but referencing each static by its full namespace
         is a serious annoyance for development; though it wouldn't be
         exposed to users of the API, doing this would make references
         longer (more to type out) and in some senses harder to read
         (more to visually parse in order to achieve an understanding
         of the logic).

         ``iii.'' is decent, but each object will maintain its own
         pointers and could lead to either unpredictable or at the
         very least hard to trace results in the event that something
         or someone deallocates those OCI structures out from
         underneath our various objects.

         ``iv.'' gets closer to a good solution, as all objects would
         basically have the same pointer, but it would induce a minor
         inconsistency in API calls between what the Connection object
         does vs. everything else, and might lead to confusion or at
         least more difficulty in validating the implementation using
         basic API symmetry.

         ``v.''  is the best and is the chosen method because if the
         top-level OCI pointers go away, they will go away for
         everyone (pointer refs) and will be easy to track in a
         debugger, it doesn't pollute any namespaces, it makes the
         variables local to each object to keep references clean,
         simple and easy to read, and maintains logical API symmetry.

       2. The error "interpretation" (asking oracle for the actual
         text of an error) was separated entirely from anything
         specific to ORAPP or its own dependencies, and is no longer
         auto-generated/auto-called after every detected failure.
         Instead, the caller learns of failures through boolean
         function/method calls and should subsequently call error() to
         get the actual error text.  This was done to simplify common
         usage and reduce model complexity (note this also follows
         standard POSIX errno-like behaviour).  Further note that the
         previous error implementation was merely a crappy corollary
         to what all the dumb Oracle demo programs do, whereas what is
         implemented now is something new.

       3. The Query object works a bit differently than before; it
         doesn't keep a state anymore, is much better at catching
         errors (esp. in the new error paradigm as described in #2),
         has a few more really useful methods for stuffing SQL queries
         into the Query object (think: format strings), and
         contemplates being parallizable in various places.

       4. The Row object also works a bit differently than before.
         Previously, a new Row object was constructed each and every
         single time Query::fetch() was called, which was pretty
         inefficient as each time a Row was constructed it would query
         for the field names, types, widths, etc, for *every* single
         fetch.  Now, instead, the Row object queries for the field
         information once, and in between Query::fetch()es simply
         resets the field values to null before calling OCIStmtFetch
         (which populates those fields).

       5. The Field object is mostly a holder for type conversions,
         since we get all values in ``char *'' form and do our own
         conversions.

       6. The dependency on a LOG object is completely _gone_.
         Instead the ORAPP library maintains an internal function
         pointer which, when non-null (set by the caller), will
         receive a more specific ``const char *'' error message when
         the library reports failures.  See the test program for an
         example of how this works.


Important Constants

    There are several constants/defines that bear acknowledgement
    since they could potentially affect the code.  They are all
    located in ``constants.hh''.

      1. ORAPP_INFO_BUFSIZ (default 1024)

        Represents intermediate buffer size used when retrieving
        non-error information from Oracle (the version string, for
        instance).

      2. ORAPP_ERR_BUFSIZ (default 1024)

        Represents intermediate buffer size used when retrieving error
        information from Oracle (ORA-ERROR-type information strings).

      3. ORAPP_FORMATSTR_BUFSIZ (default 1024)

        Represents intermediate buffer size used when doing
        format-string expansions wherever relevant.  Could potentially
        limit total size of string you're trying to insert in the
        common case where a ``%s'' format is expanded to something
        longer than ~1024 bytes.

      4. ORAPP_MAX_FIELD_WIDTH (default 100)

        Represents the maximum field width the API will allow.  This
        is to say that if you retrieve a field whose width is defined
        as something greater than this constant (e.g. 200 bytes in the
        Oracle Schema definition), the API will limit the size of
        anything retrieved and truncate to the maximum field size.

        Note that this is merely a limit; the API will only allocate
        MIN(fieldsize, max_limit) to receive actual field data, which
        will rarely be the maximum.


Converting between ORA++ and ORAPP

    The APIs are 99% identical, but for a few minor differences:

      1. OLD: Query::run()    vs.   NEW: Query::execute()

        ``run'' breaks a very common term in the DBA lexicon, and
        belongs as ``execute''.

      2. Query::reset() no longer necessary.

        ``reset'' still exists and does what is expected, but does not
        need to be explicitly called between every execution, since
        the API handles detecting and accommodating this scenario
        automatically.

      3. The LOG object is no longer necessary.

        This dependency has been removed, in favor of
        ``ORAPP::log_to()'' which allows the caller to set a function
        to receive any error messages emitted by the API.  If unset,
        no such messages are emitted.

      4. All fields are numbered starting from ``0'', not ``1''.

        Screw Oracle for changing an extremely old and common
        standard.  The new API hides this from view and returns the
        paradigm to normal by using field/column offsets that begin at
        ``0'', not ``1''.

      5. Various new features in the new API.

        These have been summarized above, but specifically there are a
        few that might be interesting:

          i. ``execute()'' has been added to both the Connection and
            Query objects, and can take a ``const char *'' now.

          ii. ``error()'' has been added to both the Connection and
            Query objects, and is able to retrieve the error state to
            a std::string.

          iii. A few more ``bind()'' types have been added.

          iv. ``Row::reset()'' now exists to clean the self-contained
            fields without deallocating them (efficiency optimization
            when reusing a Row multiple times).

      6. Must call ORAPP::Query::execute() before ORAPP::Query::fetch().

        The previous implementation (ORA++) allowed the caller to set
        the query string and then immediately call ``fetch()''.  This
        however made it difficult to distinguish between a bad query
        (``execute()''), an API error during fetch, and no data
        present to fetch from the DB.  Now ``execute()'' must always
        be called preceding a ``fetch()'', providing cleaner and
        clearer granularity of error detection.

__END__
