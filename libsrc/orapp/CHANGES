
v2.0.3   1/21/04    More compilation targets added.

    0. Added ``stdio.h'' include to ``query.cc'', in order to get at
       the ``vsnprintf()'' declaration in GCC 2.9x.
       [<EMAIL>]

    1. Converted all instances of ``std::string::clear()'' to instead
       assign an empty string.  While they accomplish the same,
       apparently the API-complete ``std::string::clear()'' was not
       yet implemented in GCC 2.9x.  [<EMAIL>]

    2. Updated ``DESIGN'' document, advising on one more issue those
       converting from ORA++ will experience.

    3. Updated ``Makefile'', removing ``-O2''.  For some odd reason,
       *only* under RedHat's GCC 2.96 (shipped with RedHat 7.2) the
       library and resulting test program segfault quickly *only* when
       optimized.  [<EMAIL>]

       As GCC 2.96 has been deprecated even by RedHat themselves,
       those using other compiler toolchains (including GCC 3.x, and
       possibly 2.95.x) who think the optimizations may provide
       additional value should re-enable them by uncommenting the
       relevant portion of ``GEN_FLAGS'' at the top of the Makefile.

    4. Minor update to the test program to add a small set of extra
       things to "test".


v2.0.2   12/16/03    Minor bugfixes.

  PLEASE NOTE: v2.0.2 was a CVS-only release.

    0. Changed ``ORAPP::Field'' constructor to allow the length of the
       name parameter to be specified, and changed object construction
       to use the ``name_len'' Oracle gives back to us (not guaranteed
       to be NULL-terminated).  [Bug #861182]

    1. Changed ``ORAPP::Row::name()'' and ``ORAPP::Row::isnull()'' to
       correct a bad assumption that an STL vector would return NULL
       when a previously unallocated index was retrieved.
       [Bug #861182]


v2.0.1   12/13/03   Initial release.


