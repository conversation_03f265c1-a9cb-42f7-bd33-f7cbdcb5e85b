cmake_minimum_required(VERSION 3.10)
project(command_alimtalk_bat C)

set(CMAKE_C_STANDARD 99)

# Source files
set(SOURCES
    src/sms_command.c
    src/sms_ctrlsub.c
    src/sms_logger.c
    src/sms_monitor.c
    src/sms_watchdog.c
    src/create_table.c
)

# Executables for alimtalk command project
add_executable(cmd_alim src/sms_command.c)
target_include_directories(cmd_alim PRIVATE inc)

add_executable(mnt_alim src/sms_monitor.c src/sms_ctrlsub.c)
target_include_directories(mnt_alim PRIVATE inc)

add_executable(log_alim src/sms_logger.c src/sms_ctrlsub.c)
target_include_directories(log_alim PRIVATE inc)

add_executable(dog_alim src/sms_watchdog.c src/sms_ctrlsub.c)
target_include_directories(dog_alim PRIVATE inc)

add_executable(crt_alim src/create_table.c)
target_include_directories(crt_alim PRIVATE inc)

# Set output directories to match original Makefile
set_target_properties(cmd_alim PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "cmd"
)

set_target_properties(mnt_alim PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "mnt"
)

set_target_properties(log_alim PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "log"
)

set_target_properties(dog_alim PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
    OUTPUT_NAME "dog"
)

set_target_properties(crt_alim PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/cfg"
    OUTPUT_NAME "crt"
)

# Add custom target for Makefile build
add_custom_target(makefile_build
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add custom target for Makefile clean
add_custom_target(makefile_clean
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)
